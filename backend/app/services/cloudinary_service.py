import logging
import os
from typing import Dict, Any, Optional, List
from io import BytesIO

import cloudinary
import cloudinary.uploader
import cloudinary.api
from fastapi import UploadFile, HTTPException

from app.core.config import settings

logger = logging.getLogger(__name__)

class CloudinaryService:
    """Service for handling image uploads to Cloudinary."""
    
    def __init__(self):
        """Initialize Cloudinary configuration."""
        cloudinary.config(
            cloud_name=self._extract_cloud_name(settings.CLOUDINARY_URL),
            api_key=settings.CLOUDINARY_API_KEY,
            api_secret=settings.CLOUDINARY_API_SECRET,
            secure=True
        )
        logger.info("Cloudinary service initialized")
    
    def _extract_cloud_name(self, cloudinary_url: str) -> str:
        """Extract cloud name from Cloudinary URL."""
        # Format: cloudinary://api_key:api_secret@cloud_name
        try:
            return cloudinary_url.split('@')[1]
        except IndexError:
            raise ValueError("Invalid Cloudinary URL format")
    
    async def upload_image(
        self, 
        file: UploadFile, 
        folder: str = "questions",
        public_id: Optional[str] = None,
        transformation: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Upload an image to Cloudinary.
        
        Args:
            file: The uploaded file
            folder: Cloudinary folder to upload to
            public_id: Optional custom public ID
            transformation: Optional transformation parameters
            
        Returns:
            Dictionary containing upload result with URL and metadata
        """
        try:
            # Validate file type
            if not self._is_valid_image(file):
                raise HTTPException(
                    status_code=400,
                    detail="Invalid file type. Only images are allowed."
                )
            
            # Read file content
            file_content = await file.read()
            
            # Prepare upload options
            upload_options = {
                "folder": folder,
                "resource_type": "image",
                "quality": "auto:good",  # Optimize quality
            }
            
            if public_id:
                upload_options["public_id"] = public_id
            
            if transformation:
                upload_options["transformation"] = transformation
            
            # Upload to Cloudinary
            result = cloudinary.uploader.upload(
                BytesIO(file_content),
                **upload_options
            )
            
            logger.info(f"Successfully uploaded image: {result.get('public_id')}")
            
            return {
                "url": result["secure_url"],
                "public_id": result["public_id"],
                "width": result.get("width"),
                "height": result.get("height"),
                "format": result.get("format"),
                "bytes": result.get("bytes"),
                "created_at": result.get("created_at")
            }
            
        except Exception as e:
            logger.error(f"Error uploading image to Cloudinary: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to upload image: {str(e)}"
            )
    
    async def upload_multiple_images(
        self, 
        files: List[UploadFile], 
        folder: str = "questions"
    ) -> List[Dict[str, Any]]:
        """
        Upload multiple images to Cloudinary.
        
        Args:
            files: List of uploaded files
            folder: Cloudinary folder to upload to
            
        Returns:
            List of upload results
        """
        results = []
        
        for file in files:
            try:
                result = await self.upload_image(file, folder)
                results.append({
                    "success": True,
                    "filename": file.filename,
                    **result
                })
            except Exception as e:
                logger.error(f"Error uploading {file.filename}: {str(e)}")
                results.append({
                    "success": False,
                    "filename": file.filename,
                    "error": str(e)
                })
        
        return results
    
    def delete_image(self, public_id: str) -> bool:
        """
        Delete an image from Cloudinary.
        
        Args:
            public_id: The public ID of the image to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = cloudinary.uploader.destroy(public_id)
            success = result.get("result") == "ok"
            
            if success:
                logger.info(f"Successfully deleted image: {public_id}")
            else:
                logger.warning(f"Failed to delete image: {public_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error deleting image from Cloudinary: {str(e)}")
            return False
    
    def get_image_url(
        self, 
        public_id: str, 
        transformation: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Get a transformed image URL.
        
        Args:
            public_id: The public ID of the image
            transformation: Optional transformation parameters
            
        Returns:
            The transformed image URL
        """
        try:
            if transformation:
                return cloudinary.CloudinaryImage(public_id).build_url(**transformation)
            else:
                return cloudinary.CloudinaryImage(public_id).build_url()
        except Exception as e:
            logger.error(f"Error building image URL: {str(e)}")
            return ""
    
    def _is_valid_image(self, file: UploadFile) -> bool:
        """
        Validate if the uploaded file is a valid image.
        
        Args:
            file: The uploaded file
            
        Returns:
            True if valid image, False otherwise
        """
        # Check file extension
        if not file.filename:
            return False
        
        valid_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'}
        file_extension = os.path.splitext(file.filename.lower())[1]
        
        if file_extension not in valid_extensions:
            return False
        
        # Check MIME type
        valid_mime_types = {
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
            'image/bmp', 'image/webp', 'image/svg+xml'
        }
        
        return file.content_type in valid_mime_types
    
    def get_optimized_transformations(self) -> Dict[str, Dict[str, Any]]:
        """
        Get predefined optimized transformations for different use cases.
        
        Returns:
            Dictionary of transformation presets
        """
        return {
            "thumbnail": {
                "width": 150,
                "height": 150,
                "crop": "fill",
                "quality": "auto:good"
            },
            "medium": {
                "width": 500,
                "height": 500,
                "crop": "limit",
                "quality": "auto:good"
            },
            "large": {
                "width": 1200,
                "height": 1200,
                "crop": "limit",
                "quality": "auto:good"
            },
            "question_display": {
                "width": 800,
                "height": 600,
                "crop": "limit",
                "quality": "auto:good"
            }
        }

# Create a singleton instance
cloudinary_service = CloudinaryService()
