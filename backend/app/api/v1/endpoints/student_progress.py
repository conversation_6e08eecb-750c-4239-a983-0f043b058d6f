from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app import schemas
from app.api import deps
from app.models.user import User, UserRole
from app.services import student_progress_service, question_service

router = APIRouter()


@router.post("/attempts", response_model=schemas.StudentQuestionAttempt)
def create_question_attempt(
    *,
    db: Session = Depends(deps.get_db),
    attempt_in: schemas.StudentQuestionAttemptCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a new question attempt.
    """
    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and attempt_in.student_id != current_user.id:
        raise HTTPException(
            status_code=400,
            detail="Student ID must match the current user"
        )

    # Check if question exists
    question = question_service.get(db, id=attempt_in.question_id)
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")

    return student_progress_service.create_question_attempt(db, obj_in=attempt_in)


@router.get("/attempts", response_model=List[schemas.StudentQuestionAttempt])
def read_question_attempts(
    *,
    db: Session = Depends(deps.get_db),
    student_id: int = Query(..., description="Student ID"),
    question_id: Optional[int] = Query(None, description="Filter by question ID"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve question attempts for a student.
    """
    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and student_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="You can only view your own attempts"
        )

    return student_progress_service.get_student_question_attempts(
        db, student_id=student_id, question_id=question_id
    )


@router.get("/attempts/course/{course_id}", response_model=List[schemas.StudentQuestionAttempt])
def read_course_attempts(
    *,
    db: Session = Depends(deps.get_db),
    course_id: int,
    student_id: int = Query(..., description="Student ID"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve question attempts for a student in a specific course.
    """
    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and student_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="You can only view your own attempts"
        )

    return student_progress_service.get_student_course_attempts(
        db, student_id=student_id, course_id=course_id
    )


@router.post("/bookmarks", response_model=schemas.StudentBookmark)
def create_bookmark(
    *,
    db: Session = Depends(deps.get_db),
    bookmark_in: schemas.StudentBookmarkCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a new bookmark.
    """
    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and bookmark_in.student_id != current_user.id:
        raise HTTPException(
            status_code=400,
            detail="Student ID must match the current user"
        )

    # Check if question exists
    question = question_service.get(db, id=bookmark_in.question_id)
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")

    return student_progress_service.create_bookmark(db, obj_in=bookmark_in)


@router.get("/bookmarks", response_model=List[schemas.StudentBookmark])
def read_bookmarks(
    *,
    db: Session = Depends(deps.get_db),
    student_id: int = Query(..., description="Student ID"),
    course_id: Optional[int] = Query(None, description="Filter by course ID"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve bookmarks for a student.
    """
    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and student_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="You can only view your own bookmarks"
        )

    return student_progress_service.get_student_bookmarks(
        db, student_id=student_id, course_id=course_id
    )


@router.put("/bookmarks/{id}", response_model=schemas.StudentBookmark)
def update_bookmark(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    bookmark_in: schemas.StudentBookmarkUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a bookmark.
    """
    bookmark = student_progress_service.get_bookmark(db, id=id)
    if not bookmark:
        raise HTTPException(status_code=404, detail="Bookmark not found")

    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and bookmark.student_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="You can only update your own bookmarks"
        )

    return student_progress_service.update_bookmark(
        db, db_obj=bookmark, obj_in=bookmark_in
    )


@router.delete("/bookmarks/{id}", response_model=schemas.StudentBookmark)
def delete_bookmark(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a bookmark.
    """
    bookmark = student_progress_service.get_bookmark(db, id=id)
    if not bookmark:
        raise HTTPException(status_code=404, detail="Bookmark not found")

    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and bookmark.student_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="You can only delete your own bookmarks"
        )

    return student_progress_service.delete_bookmark(db, id=id)


@router.post("/exams", response_model=schemas.StudentExam)
def create_exam(
    *,
    db: Session = Depends(deps.get_db),
    exam_in: schemas.StudentExamCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a new exam.
    """
    print(f"Creating exam with data: {exam_in}")
    print(f"Current user: {current_user.id} (role: {current_user.role})")

    try:
        # Ensure the student_id matches the current user if they're a student
        if current_user.role == UserRole.STUDENT and exam_in.student_id != current_user.id:
            raise HTTPException(
                status_code=400,
                detail="Student ID must match the current user"
            )

        # Validate that course_id exists, except for note-generated content
        from app.services.course_service import get as get_course

        # Special handling for note-generated content
        try:
            if getattr(exam_in, 'is_note_generated', False):
                print("Note-generated content detected, skipping course validation")

                # For note-generated content, verify that the job exists
                if getattr(exam_in, 'note_job_id', None):
                    from app.crud.crud_student_tools import mcq_generation_job
                    job = mcq_generation_job.get(db, id=exam_in.note_job_id)
                    if not job:
                        raise HTTPException(
                            status_code=404,
                            detail=f"MCQ generation job with ID {exam_in.note_job_id} not found"
                        )

                    # Verify the job belongs to the current user
                    if job.student_id != current_user.id:
                        raise HTTPException(
                            status_code=403,
                            detail="You can only create exams for your own generated content"
                        )

                    # Use the job's course name if not provided
                    if hasattr(exam_in, 'course_name') and not exam_in.course_name and job.course_name:
                        exam_in.course_name = job.course_name
                else:
                    # If note_job_id is not present, we'll just proceed without it
                    print("Warning: note_job_id not provided for note-generated content")
        except Exception as e:
            # If there's any error with the note-generated fields, log it and continue
            print(f"Warning: Error handling note-generated content: {str(e)}")

        # Handle special course ID for note-generated content
        if exam_in.course_id == -1:
            print("Special course ID -1 detected, handling as note-generated content")
            try:
                # Try to set the is_note_generated flag
                exam_in.is_note_generated = True

                # Check if we have a job ID
                if not getattr(exam_in, 'note_job_id', None):
                    print("Warning: No job ID provided for note-generated content with course_id=-1")
            except Exception as e:
                print(f"Warning: Error handling special course ID: {str(e)}")
        else:
            # Regular course validation
            course = get_course(db, id=exam_in.course_id)
            if not course:
                raise HTTPException(
                    status_code=404,
                    detail=f"Course with ID {exam_in.course_id} not found"
                )

        result = student_progress_service.create_exam(db, obj_in=exam_in)
        print(f"Exam created successfully: {result.id}")
        return result
    except Exception as e:
        print(f"Error creating exam: {str(e)}")
        raise


@router.get("/exams", response_model=List[schemas.StudentExam])
def read_exams(
    *,
    db: Session = Depends(deps.get_db),
    student_id: int = Query(..., description="Student ID"),
    course_id: Optional[int] = Query(None, description="Filter by course ID"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve exams for a student.
    """
    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and student_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="You can only view your own exams"
        )

    return student_progress_service.get_student_exams(
        db, student_id=student_id, course_id=course_id
    )


@router.get("/exams/{id}", response_model=schemas.StudentExamWithAttempts)
def read_exam_with_attempts(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve an exam with its attempts.
    """
    print(f"Retrieving exam with attempts: id={id}")
    print(f"Current user: {current_user.id} (role: {current_user.role})")

    exam = student_progress_service.get_exam(db, id=id)
    if not exam:
        print(f"Exam not found: {id}")
        raise HTTPException(status_code=404, detail="Exam not found")

    print(f"Found exam: {exam.id} (student_id: {exam.student_id})")

    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and exam.student_id != current_user.id:
        print(f"Permission denied: User {current_user.id} trying to access exam for student {exam.student_id}")
        raise HTTPException(
            status_code=403,
            detail="You can only view your own exams"
        )

    result = student_progress_service.get_exam_with_attempts(db, id=id)
    print(f"Retrieved exam with {len(result.attempts) if result.attempts else 0} attempts")
    return result


@router.put("/exams/{id}", response_model=schemas.StudentExam)
def update_exam(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    exam_in: schemas.StudentExamUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update an exam.
    """
    exam = student_progress_service.get_exam(db, id=id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and exam.student_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="You can only update your own exams"
        )

    return student_progress_service.update_exam(
        db, db_obj=exam, obj_in=exam_in
    )


@router.post("/exams/attempts", response_model=schemas.StudentExamAttempt)
def create_exam_attempt(
    *,
    db: Session = Depends(deps.get_db),
    attempt_in: schemas.StudentExamAttemptCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a new exam attempt.
    """
    print(f"Received exam attempt: {attempt_in}")
    print(f"Current user: {current_user.id} (role: {current_user.role})")

    # Check if exam exists and belongs to the current user if they're a student
    exam = student_progress_service.get_exam(db, id=attempt_in.exam_id)
    if not exam:
        print(f"Exam not found: {attempt_in.exam_id}")
        raise HTTPException(status_code=404, detail="Exam not found")

    print(f"Found exam: {exam.id} (student_id: {exam.student_id})")

    if current_user.role == UserRole.STUDENT and exam.student_id != current_user.id:
        print(f"Permission denied: User {current_user.id} trying to access exam for student {exam.student_id}")
        raise HTTPException(
            status_code=403,
            detail="You can only add attempts to your own exams"
        )

    # Check if question exists
    question = question_service.get(db, id=attempt_in.question_id)
    if not question:
        print(f"Question not found: {attempt_in.question_id}")
        raise HTTPException(status_code=404, detail="Question not found")

    print(f"Found question: {question.id}")

    # Create the exam attempt
    result = student_progress_service.create_exam_attempt(db, obj_in=attempt_in)
    print(f"Created exam attempt: {result.id}")

    return result


@router.post("/exam-attempts", response_model=None)
def create_exam_attempt_old_path(
    *,
    attempt_in: schemas.StudentExamAttemptCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Redirect from old endpoint path to new one.
    """
    print(f"WARNING: Old endpoint path '/student-progress/exam-attempts' was used")
    print(f"Received exam attempt: {attempt_in}")
    print(f"Current user: {current_user.id} (role: {current_user.role})")

    # Return a helpful error message
    raise HTTPException(
        status_code=404,
        detail="This endpoint has moved to /student-progress/exams/attempts. Please update your API calls."
    )


@router.get("/exams/{id}/topic-analysis")
def get_exam_topic_analysis(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get topic-based analysis for an exam.
    """
    # Check if exam exists
    exam = student_progress_service.get_exam(db, id=id)
    if not exam:
        raise HTTPException(status_code=404, detail="Exam not found")

    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and exam.student_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="You can only view analysis for your own exams"
        )

    return student_progress_service.get_exam_topic_analysis(db, exam_id=id)


@router.get("/courses/{course_id}/topic-analysis")
def get_course_topic_analysis(
    *,
    db: Session = Depends(deps.get_db),
    course_id: int,
    student_id: int = Query(..., description="Student ID"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get topic-based analysis for all exams in a course for a student.
    """
    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and student_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="You can only view analysis for your own exams"
        )

    return student_progress_service.get_course_topic_analysis(
        db, student_id=student_id, course_id=course_id
    )


@router.get("/stats/mcq")
def get_user_mcq_stats(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get MCQ statistics for the current user.
    """
    return student_progress_service.get_user_mcq_stats(db, user_id=current_user.id)


@router.get("/stats/performance")
def get_user_performance_data(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get performance data for the current user.
    """
    return student_progress_service.get_user_performance_data(db, user_id=current_user.id)
