from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form
from sqlalchemy.orm import Session
import csv
import io
import json
from datetime import datetime

from app import crud, models, schemas
from app.api import deps
from app.models.user import User, UserRole
from app.schemas.admin_flashcard import (
    AdminFlashCard, AdminFlashCardCreate, AdminFlashCardUpdate, AdminFlashCardWithCourse,
    AdminFlashCardCSVUploadResponse, AdminFlashCardBulkCreate, AdminFlashCardBulkUpdate, 
    AdminFlashCardBulkDelete, AdminFlashCardImageUploadResponse, AdminFlashCardStats
)
from app.services.cloudinary_service import CloudinaryService

router = APIRouter()


@router.get("/", response_model=List[AdminFlashCardWithCourse])
def read_admin_flashcards(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    course_id: Optional[int] = None,
    difficulty: Optional[str] = None,
    topic: Optional[str] = None,
    search: Optional[str] = None,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Retrieve admin flashcards with filtering options.
    """
    if search:
        flashcards = crud.admin_flashcard.search(
            db, search_term=search, skip=skip, limit=limit, course_id=course_id
        )
    else:
        flashcards = crud.admin_flashcard.get_with_course_info(
            db, skip=skip, limit=limit, course_id=course_id,
            difficulty=difficulty, topic=topic
        )

    # Add course information
    result = []
    for flashcard in flashcards:
        flashcard_dict = flashcard.__dict__.copy()
        if flashcard.course:
            flashcard_dict["course_name"] = flashcard.course.name
            flashcard_dict["course_code"] = flashcard.course.code
        result.append(AdminFlashCardWithCourse(**flashcard_dict))

    return result


@router.get("/public", response_model=List[AdminFlashCardWithCourse])
def read_public_admin_flashcards(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    course_id: Optional[int] = None,
    difficulty: Optional[str] = None,
    topic: Optional[str] = None,
    search: Optional[str] = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve admin flashcards for students to study (only active flashcards).
    """
    if search:
        flashcards = crud.admin_flashcard.search(
            db, search_term=search, skip=skip, limit=limit, course_id=course_id
        )
    else:
        flashcards = crud.admin_flashcard.get_with_course_info(
            db, skip=skip, limit=limit, course_id=course_id,
            difficulty=difficulty, topic=topic, active_only=True
        )

    # Filter only active flashcards for students
    active_flashcards = [fc for fc in flashcards if fc.is_active]

    # Add course information
    result = []
    for flashcard in active_flashcards:
        flashcard_dict = flashcard.__dict__.copy()
        if flashcard.course:
            flashcard_dict["course_name"] = flashcard.course.name
            flashcard_dict["course_code"] = flashcard.course.code
        result.append(AdminFlashCardWithCourse(**flashcard_dict))

    return result


@router.post("/", response_model=AdminFlashCard)
def create_admin_flashcard(
    *,
    db: Session = Depends(deps.get_db),
    flashcard_in: AdminFlashCardCreate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Create new admin flashcard.
    """
    # Verify course exists
    course = crud.course.get(db, id=flashcard_in.course_id)
    if not course:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Course not found"
        )
    
    flashcard = crud.admin_flashcard.create_with_creator(
        db=db, obj_in=flashcard_in, created_by=current_user.id
    )
    return flashcard


@router.get("/{id}", response_model=AdminFlashCardWithCourse)
def read_admin_flashcard(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Get admin flashcard by ID.
    """
    flashcard = crud.admin_flashcard.get(db, id=id)
    if not flashcard:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Flashcard not found"
        )
    
    # Add course information
    flashcard_dict = flashcard.__dict__.copy()
    if flashcard.course:
        flashcard_dict["course_name"] = flashcard.course.name
        flashcard_dict["course_code"] = flashcard.course.code
    
    return AdminFlashCardWithCourse(**flashcard_dict)


@router.put("/{id}", response_model=AdminFlashCard)
def update_admin_flashcard(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    flashcard_in: AdminFlashCardUpdate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Update admin flashcard.
    """
    flashcard = crud.admin_flashcard.get(db, id=id)
    if not flashcard:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Flashcard not found"
        )
    
    # If course_id is being updated, verify it exists
    if flashcard_in.course_id:
        course = crud.course.get(db, id=flashcard_in.course_id)
        if not course:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Course not found"
            )
    
    flashcard = crud.admin_flashcard.update(db, db_obj=flashcard, obj_in=flashcard_in)
    return flashcard


@router.delete("/{id}")
def delete_admin_flashcard(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Delete admin flashcard.
    """
    flashcard = crud.admin_flashcard.get(db, id=id)
    if not flashcard:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Flashcard not found"
        )
    
    flashcard = crud.admin_flashcard.remove(db, id=id)
    return {"message": "Flashcard deleted successfully"}


@router.get("/stats/overview", response_model=AdminFlashCardStats)
def get_admin_flashcard_stats(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Get admin flashcard statistics.
    """
    stats = crud.admin_flashcard.get_stats(db)
    return AdminFlashCardStats(**stats)


@router.post("/bulk", response_model=List[AdminFlashCard])
def bulk_create_admin_flashcards(
    *,
    db: Session = Depends(deps.get_db),
    flashcards_in: AdminFlashCardBulkCreate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Bulk create admin flashcards.
    """
    # Verify all courses exist
    course_ids = list(set(fc.course_id for fc in flashcards_in.flashcards))
    for course_id in course_ids:
        course = crud.course.get(db, id=course_id)
        if not course:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Course with ID {course_id} not found"
            )
    
    flashcards = crud.admin_flashcard.bulk_create(
        db=db, objs_in=flashcards_in.flashcards, created_by=current_user.id
    )
    return flashcards


@router.put("/bulk", response_model=dict)
def bulk_update_admin_flashcards(
    *,
    db: Session = Depends(deps.get_db),
    update_data: AdminFlashCardBulkUpdate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Bulk update admin flashcards.
    """
    # For now, we'll implement bulk status update
    if update_data.update_data.is_active is not None:
        updated_count = crud.admin_flashcard.bulk_update_status(
            db=db, 
            flashcard_ids=update_data.flashcard_ids, 
            is_active=update_data.update_data.is_active
        )
        return {"updated_count": updated_count, "message": "Flashcards updated successfully"}
    
    return {"updated_count": 0, "message": "No updates specified"}


@router.delete("/bulk", response_model=dict)
def bulk_delete_admin_flashcards(
    *,
    db: Session = Depends(deps.get_db),
    delete_data: AdminFlashCardBulkDelete,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Bulk delete admin flashcards.
    """
    deleted_count = crud.admin_flashcard.bulk_delete(
        db=db, flashcard_ids=delete_data.flashcard_ids
    )
    return {"deleted_count": deleted_count, "message": "Flashcards deleted successfully"}


@router.post("/upload-csv", response_model=AdminFlashCardCSVUploadResponse)
async def upload_csv_flashcards(
    *,
    db: Session = Depends(deps.get_db),
    csv_file: UploadFile = File(...),
    course_id: int = Form(...),
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Upload flashcards from CSV file.
    Expected CSV format: front_content,back_content,topic,difficulty
    """
    # Verify course exists
    course = crud.course.get(db, id=course_id)
    if not course:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Course not found"
        )

    # Validate file type
    if not csv_file.filename.endswith('.csv'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be a CSV file"
        )

    try:
        # Read CSV content
        content = await csv_file.read()
        csv_content = content.decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(csv_content))

        validation_errors = []
        creation_errors = []
        created_flashcards = []
        total_rows = 0

        for row_num, row in enumerate(csv_reader, start=2):  # Start from 2 (header is row 1)
            total_rows += 1

            # Validate required fields
            if not row.get('front_content', '').strip():
                validation_errors.append(f"Row {row_num}: front_content is required")
                continue

            if not row.get('back_content', '').strip():
                validation_errors.append(f"Row {row_num}: back_content is required")
                continue

            try:
                # Create flashcard
                flashcard_data = AdminFlashCardCreate(
                    course_id=course_id,
                    front_content=row['front_content'].strip(),
                    back_content=row['back_content'].strip(),
                    topic=row.get('topic', '').strip() or None,
                    difficulty=row.get('difficulty', '').strip() or None,
                )

                flashcard = crud.admin_flashcard.create_with_creator(
                    db=db, obj_in=flashcard_data, created_by=current_user.id
                )
                created_flashcards.append(flashcard)

            except Exception as e:
                creation_errors.append(f"Row {row_num}: {str(e)}")
                continue

        return AdminFlashCardCSVUploadResponse(
            success=len(creation_errors) == 0,
            total_rows=total_rows,
            created_flashcards=len(created_flashcards),
            validation_errors=validation_errors,
            creation_errors=creation_errors,
            flashcards=created_flashcards
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error processing CSV file: {str(e)}"
        )


@router.post("/{flashcard_id}/upload-image", response_model=AdminFlashCardImageUploadResponse)
async def upload_flashcard_image(
    *,
    db: Session = Depends(deps.get_db),
    flashcard_id: int,
    image_file: UploadFile = File(...),
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Upload image for a flashcard.
    """
    # Verify flashcard exists
    flashcard = crud.admin_flashcard.get(db, id=flashcard_id)
    if not flashcard:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Flashcard not found"
        )

    # Validate file type
    if not image_file.content_type.startswith('image/'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File must be an image"
        )

    try:
        # Initialize Cloudinary service
        cloudinary_service = CloudinaryService()

        # Upload to Cloudinary
        upload_result = await cloudinary_service.upload_image(
            image_file,
            folder="flashcards",
            public_id=f"flashcard_{flashcard_id}_{int(datetime.now().timestamp())}"
        )

        # Update flashcard with image URL
        flashcard_update = AdminFlashCardUpdate(media_url=upload_result["url"])
        crud.admin_flashcard.update(db, db_obj=flashcard, obj_in=flashcard_update)

        return AdminFlashCardImageUploadResponse(
            success=True,
            image_url=upload_result["url"],
            message="Image uploaded successfully"
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error uploading image: {str(e)}"
        )
