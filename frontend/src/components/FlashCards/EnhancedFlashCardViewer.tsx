import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Button,
  IconButton,
  CircularProgress,
  Alert,
  Chip,
  useTheme,
  useMediaQuery,
  LinearProgress,
  Tooltip,
  Card,
  CardContent,
  Fab,
} from '@mui/material';
import {
  NavigateNext as NextIcon,
  NavigateBefore as PrevIcon,
  Refresh as RefreshIcon,
  School as SchoolIcon,
  Topic as TopicIcon,
  Shuffle as ShuffleIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  TouchApp as TouchIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';

import { getFlashCards, getFlashCardGenerationStatus } from '../../api/studentTools';
import { getAdminFlashCards, getPublicAdminFlashCards } from '../../api/adminFlashcards';
import { getCourse } from '../../api/courses';
import '../../styles/FlashCard.css';

interface EnhancedFlashCardViewerProps {
  jobId?: number;
  courseId?: number;
  cardId?: number;
  adminMode?: boolean; // New prop to show admin flashcards
}

interface FlashCardData {
  id: number;
  front_content: string;
  back_content: string;
  topic?: string;
  difficulty?: string;
  course_name?: string;
  media_url?: string;
}

const EnhancedFlashCardViewer: React.FC<EnhancedFlashCardViewerProps> = ({ 
  jobId, 
  courseId, 
  cardId, 
  adminMode = false 
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // State
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [courseName, setCourseName] = useState<string | null>(null);
  const [isAutoPlay, setIsAutoPlay] = useState(false);
  const [autoPlayInterval, setAutoPlayInterval] = useState<NodeJS.Timeout | null>(null);
  const [shuffledCards, setShuffledCards] = useState<FlashCardData[]>([]);
  const [isShuffled, setIsShuffled] = useState(false);

  // Fetch flashcards based on mode
  const {
    data: flashCards = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: adminMode ? ['publicAdminFlashcards', courseId] : ['flashCards', jobId, courseId, cardId],
    queryFn: async () => {
      if (adminMode) {
        // Fetch public admin flashcards for students
        return await getPublicAdminFlashCards({
          course_id: courseId,
          limit: 100,
        });
      } else {
        // Fetch student flashcards (existing logic)
        let cards = [];
        if (cardId) {
          const allCards = await getFlashCards();
          cards = allCards.filter(card => card.id === cardId);
        } else if (jobId) {
          cards = await getFlashCards({ job_id: jobId });
        } else if (courseId) {
          cards = await getFlashCards();
          cards = cards.filter(card => card.course_name);
        } else {
          cards = await getFlashCards();
        }
        return cards;
      }
    },
  });

  // Initialize shuffled cards
  useEffect(() => {
    if (flashCards.length > 0) {
      setShuffledCards([...flashCards]);
    }
  }, [flashCards]);

  // Auto-play functionality
  useEffect(() => {
    if (isAutoPlay && shuffledCards.length > 0) {
      const interval = setInterval(() => {
        if (isFlipped) {
          handleNextCard();
        } else {
          setIsFlipped(true);
        }
      }, 3000);
      setAutoPlayInterval(interval);
      return () => clearInterval(interval);
    } else if (autoPlayInterval) {
      clearInterval(autoPlayInterval);
      setAutoPlayInterval(null);
    }
  }, [isAutoPlay, isFlipped, currentCardIndex, shuffledCards.length]);

  const currentCard = shuffledCards[currentCardIndex];

  const handleFlipCard = () => {
    setIsFlipped(!isFlipped);
  };

  const handleNextCard = () => {
    if (currentCardIndex < shuffledCards.length - 1) {
      setCurrentCardIndex(currentCardIndex + 1);
      setIsFlipped(false);
    } else {
      setCurrentCardIndex(0);
      setIsFlipped(false);
    }
  };

  const handlePrevCard = () => {
    if (currentCardIndex > 0) {
      setCurrentCardIndex(currentCardIndex - 1);
      setIsFlipped(false);
    } else {
      setCurrentCardIndex(shuffledCards.length - 1);
      setIsFlipped(false);
    }
  };

  const handleShuffle = () => {
    const shuffled = [...shuffledCards].sort(() => Math.random() - 0.5);
    setShuffledCards(shuffled);
    setCurrentCardIndex(0);
    setIsFlipped(false);
    setIsShuffled(true);
  };

  const handleAutoPlay = () => {
    setIsAutoPlay(!isAutoPlay);
  };

  const generateColor = () => {
    const colors = [
      '#667eea', '#764ba2', '#f093fb', '#f5576c',
      '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
      '#fa709a', '#fee140', '#a8edea', '#fed6e3',
    ];
    return colors[currentCardIndex % colors.length];
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'primary';
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load flashcards. Please try again.
      </Alert>
    );
  }

  if (shuffledCards.length === 0) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No flashcards available.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 }, maxWidth: 800, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ textAlign: 'center', mb: 3 }}>
        <Typography variant="h5" gutterBottom>
          {adminMode ? 'Course Flashcards' : 'Study Flashcards'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Card {currentCardIndex + 1} of {shuffledCards.length}
        </Typography>
        <LinearProgress
          variant="determinate"
          value={((currentCardIndex + 1) / shuffledCards.length) * 100}
          sx={{ mt: 1, height: 6, borderRadius: 3 }}
        />
      </Box>

      {/* Controls */}
      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 3, flexWrap: 'wrap' }}>
        <Tooltip title="Shuffle cards">
          <IconButton onClick={handleShuffle} color={isShuffled ? 'primary' : 'default'}>
            <ShuffleIcon />
          </IconButton>
        </Tooltip>
        <Tooltip title={isAutoPlay ? 'Pause auto-play' : 'Start auto-play'}>
          <IconButton onClick={handleAutoPlay} color={isAutoPlay ? 'primary' : 'default'}>
            {isAutoPlay ? <PauseIcon /> : <PlayIcon />}
          </IconButton>
        </Tooltip>
        <Tooltip title="Refresh">
          <IconButton onClick={() => refetch()}>
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Instructions */}
      <Box sx={{ textAlign: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
          <TouchIcon fontSize="small" color="action" />
          <Typography variant="body2" color="text.secondary">
            {isFlipped ? 'Showing Answer (Click to see question)' : 'Showing Question (Click to see answer)'}
          </Typography>
        </Box>
      </Box>

      {/* Flash Card */}
      <div className="card-container" onClick={handleFlipCard}>
        <div className={`card-inner ${isFlipped ? 'flipped' : ''}`}>
          {/* Front of card (Question) */}
          <div
            className="card-front"
            style={{
              background: `linear-gradient(135deg, ${generateColor()} 0%, ${theme.palette.primary.dark} 100%)`,
              color: 'white'
            }}
          >
            {currentCard?.topic && (
              <Chip
                icon={<TopicIcon fontSize="small" />}
                label={currentCard.topic}
                size="small"
                sx={{ 
                  mb: 2, 
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  '& .MuiChip-icon': { color: 'white' }
                }}
              />
            )}

            {currentCard?.media_url && (
              <Box
                component="img"
                src={currentCard.media_url}
                alt="Flashcard"
                sx={{
                  maxWidth: '100%',
                  maxHeight: 150,
                  objectFit: 'contain',
                  mb: 2,
                  borderRadius: 1,
                }}
              />
            )}

            <Typography
              variant={isMobile ? 'h6' : 'h5'}
              sx={{
                fontWeight: 'bold',
                textAlign: 'center',
                overflow: 'hidden',
                display: '-webkit-box',
                WebkitLineClamp: currentCard?.media_url ? 4 : 8,
                WebkitBoxOrient: 'vertical',
                lineHeight: 1.4,
              }}
            >
              {currentCard?.front_content}
            </Typography>
          </div>

          {/* Back of card (Answer) */}
          <div
            className="card-back"
            style={{
              background: `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.dark} 100%)`,
              color: 'white'
            }}
          >
            {currentCard?.difficulty && (
              <Chip
                label={currentCard.difficulty.toUpperCase()}
                size="small"
                sx={{ 
                  mb: 2, 
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  fontWeight: 'bold'
                }}
              />
            )}

            <Typography
              variant={isMobile ? 'h6' : 'h5'}
              sx={{
                fontWeight: 'bold',
                textAlign: 'center',
                overflow: 'hidden',
                display: '-webkit-box',
                WebkitLineClamp: 8,
                WebkitBoxOrient: 'vertical',
                lineHeight: 1.4,
              }}
            >
              {currentCard?.back_content}
            </Typography>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
        <Button
          variant="outlined"
          startIcon={<PrevIcon />}
          onClick={handlePrevCard}
          disabled={shuffledCards.length <= 1}
        >
          Previous
        </Button>

        <Box sx={{ textAlign: 'center' }}>
          {currentCard?.course_name && (
            <Chip
              icon={<SchoolIcon />}
              label={currentCard.course_name}
              variant="outlined"
              size="small"
            />
          )}
        </Box>

        <Button
          variant="outlined"
          endIcon={<NextIcon />}
          onClick={handleNextCard}
          disabled={shuffledCards.length <= 1}
        >
          Next
        </Button>
      </Box>

      {/* Auto-play indicator */}
      {isAutoPlay && (
        <Fab
          size="small"
          color="primary"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': { transform: 'scale(1)' },
              '50%': { transform: 'scale(1.1)' },
              '100%': { transform: 'scale(1)' },
            },
          }}
        >
          <PlayIcon />
        </Fab>
      )}
    </Box>
  );
};

export default EnhancedFlashCardViewer;
