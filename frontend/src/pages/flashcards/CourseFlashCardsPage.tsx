import React, { useState, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  Chip,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  School as SchoolIcon,
  Search as SearchIcon,
  Style as FlashcardsIcon,
  Topic as TopicIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';

import { getPublicAdminFlashCards } from '../../api/adminFlashcards';
import { getCourses } from '../../api/courses';
import { getDepartments, Department } from '../../api/departments';
import { getSchools, School } from '../../api/schools';
import PageHeader from '../../components/Layout/PageHeader';
import EnhancedFlashCardViewer from '../../components/FlashCards/EnhancedFlashCardViewer';

const CourseFlashCardsPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();

  // State
  const [selectedCourse, setSelectedCourse] = useState<number | null>(null);
  const [selectedSchool, setSelectedSchool] = useState<number | null>(null);
  const [selectedDepartment, setSelectedDepartment] = useState<number | ''>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string | ''>('');
  const [viewMode, setViewMode] = useState<'browse' | 'study'>('browse');

  // Fetch courses
  const { data: courses = [], isLoading: isLoadingCourses } = useQuery({
    queryKey: ['courses'],
    queryFn: () => getCourses(),
  });

  // Fetch schools
  const { data: schools = [] } = useQuery({
    queryKey: ['schools'],
    queryFn: getSchools,
  });

  // Fetch departments
  const { data: departments = [] } = useQuery({
    queryKey: ['departments'],
    queryFn: getDepartments,
  });

  // Filter courses based on school and department
  const filteredCourses = useMemo(() => {
    return courses.filter(course => {
      if (selectedSchool && course.school_id !== selectedSchool) return false;
      if (selectedDepartment && course.department_id !== selectedDepartment) return false;
      return true;
    });
  }, [courses, selectedSchool, selectedDepartment]);

  // Filter departments based on selected school
  const filteredDepartments = useMemo(() => {
    return selectedSchool
      ? departments.filter(dept => dept.school_id === selectedSchool)
      : departments;
  }, [departments, selectedSchool]);

  // Fetch flashcards
  const {
    data: flashcards = [],
    isLoading: isLoadingFlashcards,
    error,
  } = useQuery({
    queryKey: ['publicAdminFlashcards', selectedCourse, searchTerm, selectedDifficulty, selectedSchool, selectedDepartment],
    queryFn: () => {
      // If searching by department or course name, get all flashcards and filter client-side
      if (searchTerm && !selectedCourse) {
        return getPublicAdminFlashCards({
          search: searchTerm || undefined,
          difficulty: selectedDifficulty || undefined,
          limit: 500, // Get more to filter properly
        });
      }

      return getPublicAdminFlashCards({
        course_id: selectedCourse || undefined,
        search: searchTerm || undefined,
        difficulty: selectedDifficulty || undefined,
        limit: 100,
      });
    },
    enabled: Boolean(selectedCourse) || Boolean(searchTerm) || Boolean(selectedSchool) || Boolean(selectedDepartment),
  });

  // Filter flashcards based on search term (including department and course names)
  const filteredFlashcards = useMemo(() => {
    if (!searchTerm) return flashcards;

    const searchLower = searchTerm.toLowerCase();
    return flashcards.filter(flashcard => {
      const course = courses.find(c => c.id === flashcard.course_id);
      const department = course ? departments.find(d => d.id === course.department_id) : null;

      return (
        flashcard.front_content?.toLowerCase().includes(searchLower) ||
        flashcard.back_content?.toLowerCase().includes(searchLower) ||
        flashcard.topic?.toLowerCase().includes(searchLower) ||
        flashcard.course_name?.toLowerCase().includes(searchLower) ||
        course?.name?.toLowerCase().includes(searchLower) ||
        course?.code?.toLowerCase().includes(searchLower) ||
        department?.name?.toLowerCase().includes(searchLower)
      );
    });
  }, [flashcards, searchTerm, courses, departments]);

  // Group filtered flashcards by course
  const flashcardsByCourse = useMemo(() => {
    return filteredFlashcards.reduce((acc, flashcard) => {
      const courseKey = flashcard.course_name || 'Unknown Course';
      if (!acc[courseKey]) {
        acc[courseKey] = [];
      }
      acc[courseKey].push(flashcard);
      return acc;
    }, {} as Record<string, typeof filteredFlashcards>);
  }, [filteredFlashcards]);

  const handleCourseSelect = (courseId: number) => {
    setSelectedCourse(courseId);
    setViewMode('study');
  };

  const handleBackToBrowse = () => {
    setViewMode('browse');
    setSelectedCourse(null);
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'default';
    }
  };

  if (viewMode === 'study' && selectedCourse) {
    return (
      <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
        <Box sx={{ p: { xs: 2, md: 3 } }}>
          <PageHeader
            title="Study Flashcards"
            subtitle={courses.find(c => c.id === selectedCourse)?.name || 'Course Flashcards'}
            action={
              <Box>
                <Typography
                  variant="body2"
                  color="primary"
                  sx={{ cursor: 'pointer', textDecoration: 'underline' }}
                  onClick={handleBackToBrowse}
                >
                  ← Back to Browse
                </Typography>
              </Box>
            }
          />
        </Box>
        <EnhancedFlashCardViewer courseId={selectedCourse} adminMode={true} />
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <PageHeader
        title="Course Flashcards"
        subtitle="Study flashcards organized by course"
        icon={<FlashcardsIcon />}
      />

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              fullWidth
              placeholder="Search by department, course, or content..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              size="small"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>School</InputLabel>
              <Select
                value={selectedSchool || ''}
                onChange={(e) => {
                  const schoolId = e.target.value ? Number(e.target.value) : null;
                  setSelectedSchool(schoolId);
                  setSelectedDepartment(null);
                  setSelectedCourse(null);
                }}
                label="School"
              >
                <MenuItem value="">All Schools</MenuItem>
                {schools.map((school) => (
                  <MenuItem key={school.id} value={school.id}>
                    {school.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Department</InputLabel>
              <Select
                value={selectedDepartment || ''}
                onChange={(e) => {
                  const deptId = e.target.value ? Number(e.target.value) : null;
                  setSelectedDepartment(deptId);
                  setSelectedCourse(null);
                }}
                label="Department"
                disabled={!selectedSchool}
              >
                <MenuItem value="">All Departments</MenuItem>
                {filteredDepartments.map((dept) => (
                  <MenuItem key={dept.id} value={dept.id}>
                    {dept.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={2}>
            <FormControl fullWidth size="small">
              <InputLabel>Course</InputLabel>
              <Select
                value={selectedCourse || ''}
                onChange={(e) => setSelectedCourse(e.target.value ? Number(e.target.value) : null)}
                label="Course"
                disabled={isLoadingCourses}
              >
                <MenuItem value="">All Courses</MenuItem>
                {filteredCourses.map((course) => (
                  <MenuItem key={course.id} value={course.id}>
                    {course.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel>Difficulty</InputLabel>
              <Select
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value as string)}
                label="Difficulty"
              >
                <MenuItem value="">All Levels</MenuItem>
                <MenuItem value="easy">Easy</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="hard">Hard</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Content */}
      {isLoadingFlashcards ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error">
          Failed to load flashcards. Please try again.
        </Alert>
      ) : filteredFlashcards.length === 0 ? (
        <Alert severity="info">
          {selectedCourse || searchTerm || selectedSchool || selectedDepartment
            ? 'No flashcards found matching your criteria.'
            : 'Select filters or search to view flashcards.'}
        </Alert>
      ) : (
        <Box>
          {/* Course Groups */}
          {Object.entries(flashcardsByCourse).map(([courseName, courseFlashcards], index) => (
            <motion.div
              key={courseName}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Paper sx={{ p: 3, mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <SchoolIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6" sx={{ flexGrow: 1 }}>
                    {courseName}
                  </Typography>
                  <Chip
                    label={`${courseFlashcards.length} cards`}
                    color="primary"
                    variant="outlined"
                    size="small"
                  />
                </Box>

                <Grid container spacing={2}>
                  {courseFlashcards.slice(0, 6).map((flashcard) => (
                    <Grid item xs={12} sm={6} md={4} key={flashcard.id}>
                      <Card
                        sx={{
                          height: '100%',
                          transition: 'transform 0.2s, box-shadow 0.2s',
                          '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: theme.shadows[8],
                          },
                        }}
                      >
                        <CardActionArea
                          sx={{ height: '100%' }}
                          onClick={() => handleCourseSelect(flashcard.course_id)}
                        >
                          <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                              {flashcard.topic && (
                                <Chip
                                  icon={<TopicIcon fontSize="small" />}
                                  label={flashcard.topic}
                                  size="small"
                                  variant="outlined"
                                />
                              )}
                              {flashcard.difficulty && (
                                <Chip
                                  label={flashcard.difficulty}
                                  size="small"
                                  color={getDifficultyColor(flashcard.difficulty) as any}
                                />
                              )}
                            </Box>

                            <Typography
                              variant="body1"
                              sx={{
                                fontWeight: 'bold',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                display: '-webkit-box',
                                WebkitLineClamp: 3,
                                WebkitBoxOrient: 'vertical',
                                mb: 1,
                                flexGrow: 1,
                              }}
                            >
                              {flashcard.front_content}
                            </Typography>

                            {flashcard.media_url && (
                              <Chip
                                icon={<FlashcardsIcon fontSize="small" />}
                                label="Has Image"
                                size="small"
                                variant="outlined"
                                sx={{ alignSelf: 'flex-start' }}
                              />
                            )}
                          </CardContent>
                        </CardActionArea>
                      </Card>
                    </Grid>
                  ))}
                </Grid>

                {courseFlashcards.length > 6 && (
                  <Box sx={{ textAlign: 'center', mt: 2 }}>
                    <Typography
                      variant="body2"
                      color="primary"
                      sx={{ cursor: 'pointer', textDecoration: 'underline' }}
                      onClick={() => {
                        const course = courses.find(c => c.name === courseName);
                        if (course) handleCourseSelect(course.id);
                      }}
                    >
                      View all {courseFlashcards.length} flashcards →
                    </Typography>
                  </Box>
                )}
              </Paper>
            </motion.div>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default CourseFlashCardsPage;
