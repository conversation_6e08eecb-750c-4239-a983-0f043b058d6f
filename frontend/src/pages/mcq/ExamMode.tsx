import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Alert,
  AlertTitle,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  Stepper,
  Step,
  StepLabel,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  useTheme,
  Card,
  CardContent,
  Chip,
  LinearProgress,
  Divider,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Check as CheckIcon,
  Warning as WarningIcon,
  Timer as TimerIcon,
  Flag as FlagIcon,
  Home as HomeIcon,
} from '@mui/icons-material';
import { useParams, useNavigate, useBeforeUnload, useSearchParams } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';

import { useAuth } from '../../contexts/AuthContext';
import { getQuestions, getQuestion, Question } from '../../api/questions';
import { getCourse } from '../../api/courses';
import {
  createExam,
  createExamAttempt,
  updateExam,
  getExamWithAttempts,
  ExamCreate,
  ExamAttemptCreate,
  ExamUpdate,
} from '../../api/studentProgress';
import { getMCQGenerationStatus } from '../../api/studentTools';
import { ProcessingStatus } from '../../types/studentTools';
import DraggableCalculator from '../../components/DraggableCalculator';
import ExamResultsAnalytics from '../../components/ExamResults/ExamResultsAnalytics';
import MathMarkdown from '../../components/common/MathMarkdown';

const MotionPaper = motion(Paper);
const MotionCard = motion(Card);

// Simplified navigation blocker using only beforeunload
const useBlockNavigation = (shouldBlock: boolean) => {
  // Set up the beforeunload event
  useBeforeUnload(
    React.useCallback(
      (event) => {
        if (shouldBlock) {
          event.preventDefault();
          // Chrome requires returnValue to be set
          event.returnValue = '';
          return 'You have an active exam in progress. If you leave, your progress will be saved, but you should return to complete it.';
        }
      },
      [shouldBlock]
    )
  );

  // Use a custom dialog for back button and URL changes
  useEffect(() => {
    // Function to handle popstate events (back/forward buttons)
    const handlePopState = (event: PopStateEvent) => {
      if (shouldBlock) {
        // Prevent the default action
        event.preventDefault();

        // Show a confirmation dialog
        const confirmNavigation = window.confirm(
          "You have an active exam in progress. Leaving will submit your current answers and end the exam session. Are you sure you want to leave?"
        );

        if (confirmNavigation) {
          // Force submit the exam before leaving
          window.dispatchEvent(new CustomEvent('forceSubmitExam'));
        } else {
          // If the user cancels, push a new state to prevent navigation
          window.history.pushState(null, '', window.location.href);
        }
      }
    };

    // Add event listener for popstate
    window.addEventListener('popstate', handlePopState);

    // Push a state to enable popstate detection
    if (shouldBlock) {
      window.history.pushState(null, '', window.location.href);
    }

    // Clean up
    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [shouldBlock]);
};

const ExamMode: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { courseId } = useParams<{ courseId: string }>();
  const [searchParams] = useSearchParams();
  const noteGenerated = searchParams.get('noteGenerated') === 'true';
  const jobId = searchParams.get('jobId');
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Exam state
  const [examId, setExamId] = useState<number | null>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<number, string>>({});
  const [flaggedQuestions, setFlaggedQuestions] = useState<number[]>([]);
  const [examStarted, setExamStarted] = useState(false);
  const [examCompleted, setExamCompleted] = useState(false);
  const [examResults, setExamResults] = useState<{
    score: number;
    correctAnswers: number;
    totalQuestions: number;
    examId: number;
    courseId: number;
    timestamp: string;
  } | null>(null);

  // Time tracking state
  const [questionStartTime, setQuestionStartTime] = useState<number>(Date.now());
  const [timeSpent, setTimeSpent] = useState<Record<number, number>>({});

  // Exam start timestamp for timer calculations
  const [examStartTimestamp, setExamStartTimestamp] = useState<number | null>(null);

  // Exam configuration
  const [examTimeLimit, setExamTimeLimit] = useState<number>(60); // Default: 60 minutes
  const [examQuestionCount, setExamQuestionCount] = useState<number>(60); // Default: 60 questions

  // Timer state
  const [timeRemaining, setTimeRemaining] = useState<number>(60 * 60); // 60 minutes in seconds
  const [timerWarning, setTimerWarning] = useState(false);

  // Use the navigation blocker
  useBlockNavigation(examStarted && !examCompleted);

  // Handle force submit event
  useEffect(() => {
    const handleForceSubmit = () => {
      if (examStarted && !examCompleted) {
        console.log("Force submit triggered by navigation attempt");
        submitExam();
      }
    };

    window.addEventListener('forceSubmitExam', handleForceSubmit);
    return () => {
      window.removeEventListener('forceSubmitExam', handleForceSubmit);
    };
  }, [examStarted, examCompleted]);

  // Dialog state
  const [showStartDialog, setShowStartDialog] = useState(true);
  const [showSubmitDialog, setShowSubmitDialog] = useState(false);
  const [showTimeUpDialog, setShowTimeUpDialog] = useState(false);

  // Track exam creation state
  const [creatingExam, setCreatingExam] = useState(false);

  // Log dialog state changes for debugging
  useEffect(() => {
    console.log("Submit dialog state changed:", showSubmitDialog);
  }, [showSubmitDialog]);

  // Fetch course
  const {
    data: course,
    isLoading: isLoadingCourse,
    error: courseError,
  } = useQuery({
    queryKey: ['course', courseId],
    queryFn: () => getCourse(parseInt(courseId!)),
    enabled: !!courseId && courseId !== 'note-generated',
  });

  // Fetch all available questions - either from course or from note-generated job
  const {
    data: allQuestions = [],
    isLoading: isLoadingQuestions,
    error: questionsError,
  } = useQuery({
    queryKey: ['questions', courseId, 'multiple_choice', 'exam', jobId],
    queryFn: async () => {
      if (jobId) {
        // If this is a note-generated MCQ session, get the job status to get the questions
        const jobStatus = await getMCQGenerationStatus(parseInt(jobId));
        console.log('Job status for exam mode:', jobStatus);

        if (jobStatus.status === ProcessingStatus.COMPLETED && jobStatus.generated_question_ids) {
          // Fetch each question individually
          const questionPromises = jobStatus.generated_question_ids.map(id => getQuestion(id));
          const questions = await Promise.all(questionPromises);
          console.log(`Fetched ${questions.length} questions for job ID ${jobId} in exam mode`);
          return questions;
        }
        console.warn(`Job ${jobId} is not completed or has no questions for exam mode`);
        return [];
      } else if (courseId && courseId !== 'note-generated') {
        // Regular course questions
        console.log(`Fetching questions for course ID ${courseId} in exam mode`);
        const questions = await getQuestions(
          parseInt(courseId!),
          undefined,
          'multiple_choice'
        );
        console.log(`Fetched ${questions.length} questions for course ID ${courseId} in exam mode`);
        return questions;
      } else {
        console.warn('No valid course ID or job ID provided for exam mode');
        return [];
      }
    },
    enabled: !!(courseId || jobId),
  });

  // Limit questions based on examQuestionCount
  const questions = React.useMemo(() => {
    if (allQuestions.length === 0) return [];

    // If examQuestionCount is less than the total questions, limit the number
    if (examQuestionCount < allQuestions.length) {
      // Shuffle the questions and take the first examQuestionCount
      return [...allQuestions]
        .sort(() => Math.random() - 0.5)
        .slice(0, examQuestionCount);
    }

    // Otherwise, return all questions
    return allQuestions;
  }, [allQuestions, examQuestionCount]);

  // Create exam mutation
  const createExamMutation = useMutation({
    mutationFn: (data: ExamCreate) => createExam(data),
    onSuccess: (data) => {
      // Update the cache with the new exam
      queryClient.setQueryData(['exam', data.id], data);
    }
  });

  // Update exam mutation
  const updateExamMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: ExamUpdate }) => updateExam(id, data),
    onSuccess: (updatedExam, variables) => {
      // Update the cache with the updated exam
      queryClient.setQueryData(['exam', variables.id], (oldData: any) => {
        return { ...oldData, ...updatedExam };
      });
    }
  });

  // Create exam attempt mutation
  const createExamAttemptMutation = useMutation({
    mutationFn: (data: ExamAttemptCreate) => createExamAttempt(data),
    onSuccess: (data, variables) => {
      // Update the exam with attempts cache if it exists
      queryClient.setQueryData(['exam', variables.exam_id], (oldData: any) => {
        if (!oldData) return oldData;

        // If the exam has attempts, add the new attempt
        if (oldData.attempts) {
          return {
            ...oldData,
            attempts: [...oldData.attempts, data]
          };
        }

        return oldData;
      });
    }
  });

  // Format time remaining
  const formatTimeRemaining = useCallback(() => {
    const minutes = Math.floor(timeRemaining / 60);
    const seconds = timeRemaining % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, [timeRemaining]);

  // Save timer state to localStorage
  useEffect(() => {
    if (examStarted && !examCompleted) {
      localStorage.setItem('examTimeRemaining', timeRemaining.toString());
    }
  }, [examStarted, examCompleted, timeRemaining]);

  // Timer effect
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (examStarted && !examCompleted) {
      timer = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 0) {
            clearInterval(timer);
            setShowTimeUpDialog(true);
            return 0;
          }

          // Show warning when 5 minutes remaining
          if (prev === 300) {
            setTimerWarning(true);
          }

          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (timer) clearInterval(timer);
    };
  }, [examStarted, examCompleted]);

  // Get current question
  const currentQuestion = questions && questions.length > 0 ? questions[currentQuestionIndex] : null;

  // Update question start time when current question changes
  useEffect(() => {
    if (examStarted && !examCompleted && currentQuestion) {
      setQuestionStartTime(Date.now());
    }
  }, [currentQuestionIndex, examStarted, examCompleted, currentQuestion]);

  // Function to record time spent on current question
  const recordTimeSpent = useCallback(() => {
    if (!currentQuestion || !examStarted || examCompleted) return;

    const questionId = currentQuestion.id;
    const currentTime = Date.now();
    const timeElapsed = Math.floor((currentTime - questionStartTime) / 1000); // Convert to seconds

    // Add to existing time or set new time
    const existingTime = timeSpent[questionId] || 0;
    const totalTime = existingTime + timeElapsed;

    console.log(`Recording time spent on question ${questionId}: ${timeElapsed}s (total: ${totalTime}s)`);

    // Only update if time has actually elapsed to prevent infinite loops
    if (timeElapsed > 0) {
      setTimeSpent(prev => ({
        ...prev,
        [questionId]: totalTime
      }));

      // Reset start time for next recording
      setQuestionStartTime(currentTime);
    }
  }, [currentQuestion, questionStartTime, timeSpent, examStarted, examCompleted]);

  // Check for existing exam in localStorage on component mount
  useEffect(() => {
    // Check for exam configuration in sessionStorage
    const storedExamConfig = sessionStorage.getItem('examConfig');
    if (storedExamConfig) {
      try {
        const examConfig = JSON.parse(storedExamConfig);
        if (examConfig.timeLimit) {
          setExamTimeLimit(examConfig.timeLimit);
          setTimeRemaining(examConfig.timeLimit * 60); // Convert to seconds
        }
        if (examConfig.questionCount) {
          setExamQuestionCount(examConfig.questionCount);
        }
        // Clear the stored config after reading it
        sessionStorage.removeItem('examConfig');
      } catch (e) {
        console.error("Error parsing stored exam config:", e);
      }
    }

    // Check for exam state in localStorage immediately
    const storedExamId = localStorage.getItem('currentExamId');
    const storedExamStarted = localStorage.getItem('examStarted');
    const storedTimeSpent = localStorage.getItem('timeSpent');
    const storedTimeRemaining = localStorage.getItem('examTimeRemaining');
    const storedExamStartTimestamp = localStorage.getItem('examStartTimestamp');

    // If we have an active exam in localStorage, set a flag to show loading state
    if (storedExamId && storedExamStarted === 'true') {
      console.log("Found active exam in localStorage, waiting for questions to load");
      setCreatingExam(true); // Show loading state while we wait for questions

      // Restore time spent data if available
      if (storedTimeSpent) {
        try {
          setTimeSpent(JSON.parse(storedTimeSpent));
        } catch (e) {
          console.error("Error parsing stored time spent:", e);
        }
      }

      // Restore timer state if available
      if (storedTimeRemaining) {
        try {
          const remainingTime = parseInt(storedTimeRemaining);
          if (!isNaN(remainingTime) && remainingTime > 0) {
            setTimeRemaining(remainingTime);
          }
        } catch (e) {
          console.error("Error parsing stored time remaining:", e);
        }
      }

      // Restore exam start timestamp if available
      if (storedExamStartTimestamp) {
        try {
          const startTimestamp = parseInt(storedExamStartTimestamp);
          if (!isNaN(startTimestamp)) {
            setExamStartTimestamp(startTimestamp);
          }
        } catch (e) {
          console.error("Error parsing stored exam start timestamp:", e);
        }
      }
    }
  }, []);

  // Restore exam state once questions are loaded
  useEffect(() => {
    // Only restore if we have questions loaded
    if (questions.length === 0 || isLoadingQuestions) {
      return;
    }

    const storedExamId = localStorage.getItem('currentExamId');
    const storedExamStarted = localStorage.getItem('examStarted');
    const storedCurrentQuestionIndex = localStorage.getItem('currentQuestionIndex');
    const storedAnswers = localStorage.getItem('examAnswers');
    const storedFlaggedQuestions = localStorage.getItem('flaggedQuestions');
    const storedTimeRemaining = localStorage.getItem('examTimeRemaining');
    const storedExamStartTimestamp = localStorage.getItem('examStartTimestamp');
    const storedQuestionCount = localStorage.getItem('examQuestionCount');

    console.log("Checking localStorage with loaded questions:", {
      storedExamId,
      storedExamStarted,
      storedCurrentQuestionIndex,
      hasStoredAnswers: !!storedAnswers,
      hasStoredFlaggedQuestions: !!storedFlaggedQuestions,
      hasStoredTimeRemaining: !!storedTimeRemaining,
      hasStoredExamStartTimestamp: !!storedExamStartTimestamp,
      questionsLoaded: questions.length
    });

    if (storedExamId && storedExamStarted === 'true' && !examCompleted) {
      console.log("Restoring exam state from localStorage");

      try {
        // Update state in a specific order
        setExamId(parseInt(storedExamId));

        if (storedCurrentQuestionIndex) {
          const index = parseInt(storedCurrentQuestionIndex);
          // Make sure the index is valid
          if (index >= 0 && index < questions.length) {
            setCurrentQuestionIndex(index);
          } else {
            setCurrentQuestionIndex(0);
          }
        }

        if (storedAnswers) {
          try {
            setAnswers(JSON.parse(storedAnswers));
          } catch (e) {
            console.error("Error parsing stored answers:", e);
          }
        }

        if (storedFlaggedQuestions) {
          try {
            setFlaggedQuestions(JSON.parse(storedFlaggedQuestions));
          } catch (e) {
            console.error("Error parsing stored flagged questions:", e);
          }
        }

        // Restore timer state if available
        if (storedTimeRemaining) {
          try {
            const remainingTime = parseInt(storedTimeRemaining);
            if (!isNaN(remainingTime) && remainingTime > 0) {
              console.log("Restoring timer state:", remainingTime);
              setTimeRemaining(remainingTime);

              // Calculate time limit in minutes from remaining time
              const estimatedTimeLimit = Math.ceil(remainingTime / 60);
              setExamTimeLimit(estimatedTimeLimit);
            }
          } catch (e) {
            console.error("Error parsing stored time remaining:", e);
          }
        }

        // Restore question count if available
        if (storedQuestionCount) {
          try {
            const questionCount = parseInt(storedQuestionCount);
            if (!isNaN(questionCount) && questionCount > 0) {
              console.log("Restoring question count:", questionCount);
              setExamQuestionCount(questionCount);
            }
          } catch (e) {
            console.error("Error parsing stored question count:", e);
          }
        }

        // Restore exam start timestamp if available
        if (storedExamStartTimestamp) {
          try {
            const startTimestamp = parseInt(storedExamStartTimestamp);
            if (!isNaN(startTimestamp)) {
              console.log("Restoring exam start timestamp:", new Date(startTimestamp).toISOString());
              setExamStartTimestamp(startTimestamp);
            }
          } catch (e) {
            console.error("Error parsing stored exam start timestamp:", e);
          }
        }

        // Set these last to trigger UI updates
        setShowStartDialog(false);

        // Use setTimeout to ensure state updates are processed
        setTimeout(() => {
          setExamStarted(true);
          setCreatingExam(false); // Hide loading state
          console.log("Exam state restored successfully");
        }, 100);
      } catch (error) {
        console.error("Error restoring exam state:", error);
        setCreatingExam(false);
      }
    } else {
      // No active exam in localStorage, make sure we're not showing loading state
      setCreatingExam(false);
    }
  }, [questions, isLoadingQuestions, examCompleted]);

  // Save current question index to localStorage when it changes
  useEffect(() => {
    if (examStarted && !examCompleted) {
      // Record time spent when changing questions, but only if we have a valid question
      if (currentQuestion) {
        // Use setTimeout to avoid immediate state updates that could cause loops
        setTimeout(() => {
          recordTimeSpent();
        }, 0);
      }
      localStorage.setItem('currentQuestionIndex', currentQuestionIndex.toString());
    }
  }, [examStarted, examCompleted, currentQuestionIndex]);

  // Save answers to localStorage when they change
  useEffect(() => {
    if (examStarted && !examCompleted) {
      localStorage.setItem('examAnswers', JSON.stringify(answers));
    }
  }, [examStarted, examCompleted, answers]);

  // Save flagged questions to localStorage when they change
  useEffect(() => {
    if (examStarted && !examCompleted) {
      localStorage.setItem('flaggedQuestions', JSON.stringify(flaggedQuestions));
    }
  }, [examStarted, examCompleted, flaggedQuestions]);

  // Save time spent to localStorage when it changes
  useEffect(() => {
    if (examStarted && !examCompleted) {
      localStorage.setItem('timeSpent', JSON.stringify(timeSpent));
    }
  }, [examStarted, examCompleted, timeSpent]);

  // Handle case when questions are loading or not available
  useEffect(() => {
    if (!isLoadingQuestions && questions.length === 0) {
      console.log("No questions available for this course for exam mode");
    } else if (!isLoadingQuestions && questions.length > 0) {
      console.log(`Loaded ${questions.length} questions for exam mode`);
    }
  }, [isLoadingQuestions, questions]);

  // Prevent navigation away from the exam
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (examStarted && !examCompleted) {
        // Standard way of showing a confirmation dialog before leaving the page
        e.preventDefault();
        e.returnValue = '';
        return '';
      }
    };

    // Add event listener for beforeunload
    window.addEventListener('beforeunload', handleBeforeUnload);

    // Clean up the event listener
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [examStarted, examCompleted]);

  // We'll use the beforeunload event to handle navigation blocking



  // Debug log component state
  useEffect(() => {
    console.log("Component state:", {
      examStarted,
      examId,
      showStartDialog,
      currentQuestionIndex,
      questions: questions.length,
      currentQuestion: currentQuestion ? currentQuestion.id : null,
      examCompleted,
      creatingExam
    });
  }, [examStarted, examId, showStartDialog, currentQuestionIndex, questions, currentQuestion, examCompleted, creatingExam]);

  // Start exam with configuration
  const startExam = async (config?: { timeLimit: number; questionCount: number }) => {
    console.log("Starting exam with:", {
      user,
      courseId,
      questionsCount: questions.length,
      timeLimit: config?.timeLimit || examTimeLimit,
      questionCount: config?.questionCount || examQuestionCount
    });

    if (!user || (!courseId && !jobId) || questions.length === 0) {
      alert("Cannot start exam: missing user, course information, or questions");
      console.error("Cannot start exam: missing user, course information, or questions");
      console.error("Details:", { user, courseId, jobId, questionsLength: questions.length });
      return;
    }

    try {
      // Set creating state to true
      setCreatingExam(true);

      // Update exam configuration if provided
      if (config) {
        setExamTimeLimit(config.timeLimit);
        setExamQuestionCount(config.questionCount);
      }

      // Determine actual question count (min of available questions and requested count)
      const actualQuestionCount = Math.min(
        config?.questionCount || examQuestionCount,
        questions.length
      );

      // Calculate time limit in seconds
      const timeLimitInSeconds = (config?.timeLimit || examTimeLimit) * 60;

      // Update timer state
      setTimeRemaining(timeLimitInSeconds);

      // Create exam in backend
      // Validate data before creating exam
      if (!user?.id) {
        console.error("Cannot create exam: user ID is missing");
        throw new Error("User ID is required to create an exam");
      }

      // For note-generated content, we need to handle the course ID differently
      let parsedCourseId: number;
      let isNoteGenerated = false;
      let noteJobId: number | null = null;
      let courseName: string | null = null;

      if (noteGenerated && jobId) {
        // For note-generated content, we'll use the job ID to identify the content
        try {
          // Get the job status to verify it exists
          const jobStatus = await getMCQGenerationStatus(parseInt(jobId));
          if (jobStatus) {
            // Set flag to indicate this is note-generated content
            isNoteGenerated = true;
            noteJobId = parseInt(jobId);

            // Store the job ID in localStorage for reference
            localStorage.setItem('lastNoteJobId', noteJobId.toString());

            // Get the first course ID from the database to use as a placeholder
            // This is a workaround to avoid database schema changes
            if (jobStatus.course_name) {
              courseName = jobStatus.course_name;
              localStorage.setItem('lastNoteCourseName', courseName);
            } else {
              courseName = "Generated Questions";
              localStorage.setItem('lastNoteCourseName', courseName);
            }

            // Use the first course ID as a placeholder
            // We'll use 1 as a fallback if we can't get a valid course ID
            parsedCourseId = 1;
            console.log("Using note-generated content with job ID:", noteJobId);
          } else {
            console.error("Job status not found for job ID:", jobId);
            throw new Error("Could not find job information for note-generated content");
          }
        } catch (error) {
          console.error("Error getting job status:", error);
          throw new Error("Could not get job information for note-generated content");
        }
      } else if (!courseId) {
        console.error("Cannot create exam: course ID is missing");
        throw new Error("Course ID is required to create an exam");
      } else if (courseId === 'note-generated') {
        // Handle the case where courseId is 'note-generated'
        if (!jobId) {
          console.error("Cannot create exam: note-generated course requires a job ID");
          throw new Error("Job ID is required for note-generated content");
        }

        // For note-generated content with courseId='note-generated', use the job ID
        try {
          // Get the job status to verify it exists
          const jobStatus = await getMCQGenerationStatus(parseInt(jobId));
          if (jobStatus) {
            // Set flag to indicate this is note-generated content
            isNoteGenerated = true;
            noteJobId = parseInt(jobId);

            // Store the job ID in localStorage for reference
            localStorage.setItem('lastNoteJobId', noteJobId.toString());

            // Get the course name if available
            if (jobStatus.course_name) {
              courseName = jobStatus.course_name;
              localStorage.setItem('lastNoteCourseName', courseName);
            } else {
              courseName = "Generated Questions";
              localStorage.setItem('lastNoteCourseName', courseName);
            }

            // Use the first course ID as a placeholder
            // We'll use 1 as a fallback if we can't get a valid course ID
            parsedCourseId = 1;
            console.log("Using note-generated content with job ID:", noteJobId);
          } else {
            console.error("Job status not found for job ID:", jobId);
            throw new Error("Could not find job information for note-generated content");
          }
        } catch (error) {
          console.error("Error getting job status:", error);
          throw new Error("Could not get job information for note-generated content");
        }
      } else {
        // Regular course ID
        parsedCourseId = parseInt(courseId);
        if (isNaN(parsedCourseId)) {
          console.error("Cannot create exam: course ID is not a valid number", courseId);
          throw new Error("Course ID must be a valid number");
        }
      }

      // Ensure question count is valid
      if (actualQuestionCount <= 0) {
        console.error("Cannot create exam: question count must be greater than 0");
        throw new Error("Question count must be greater than 0");
      }

      // Create exam data with note-generated fields
      const examData: any = {
        student_id: user.id,
        course_id: parsedCourseId,
        total_questions: actualQuestionCount,
      };

      // Add note-generated fields if applicable
      if (isNoteGenerated && noteJobId) {
        examData.is_note_generated = true;
        examData.note_job_id = noteJobId;
        if (courseName) {
          examData.course_name = courseName;
        }
      }

      // Store note-generated information in localStorage
      if (isNoteGenerated && noteJobId) {
        localStorage.setItem('isNoteGenerated', 'true');
        localStorage.setItem('noteJobId', noteJobId.toString());
        if (courseName) {
          localStorage.setItem('noteCourseName', courseName);
        }
      } else {
        localStorage.removeItem('isNoteGenerated');
        localStorage.removeItem('noteJobId');
        localStorage.removeItem('noteCourseName');
      }

      console.log("Creating exam with data:", examData);

      // Create the exam using the API
      let exam;
      try {
        // Add a retry mechanism with exponential backoff
        const maxRetries = 3;
        let retryCount = 0;
        let success = false;

        while (!success && retryCount < maxRetries) {
          try {
            exam = await createExamMutation.mutateAsync(examData);
            console.log("Exam created successfully:", exam);
            success = true;
          } catch (error) {
            retryCount++;
            console.error(`Error creating exam (attempt ${retryCount}/${maxRetries}):`, error);

            if (retryCount < maxRetries) {
              // Wait with exponential backoff before retrying
              const waitTime = Math.pow(2, retryCount) * 1000;
              console.log(`Retrying in ${waitTime}ms...`);
              await new Promise(resolve => setTimeout(resolve, waitTime));
            } else {
              throw error;
            }
          }
        }
      } catch (error) {
        console.error("All retry attempts failed:", error);
        throw error;
      }

      // Store exam ID and timestamp in localStorage to persist across refreshes
      const currentTimestamp = Date.now();
      localStorage.setItem('currentExamId', exam.id.toString());
      localStorage.setItem('examStarted', 'true');
      localStorage.setItem('currentQuestionIndex', '0');
      localStorage.setItem('examStartTimestamp', currentTimestamp.toString());
      localStorage.setItem('examTimeRemaining', timeLimitInSeconds.toString());
      localStorage.setItem('examQuestionCount', actualQuestionCount.toString());

      // Set the exam start timestamp
      setExamStartTimestamp(currentTimestamp);

      // First close the dialog
      setShowStartDialog(false);

      // Wait a moment to ensure the dialog is closed
      setTimeout(() => {
        // Then update the exam state
        setExamId(exam.id);
        setCurrentQuestionIndex(0);

        // Finally, start the exam
        setTimeout(() => {
          setExamStarted(true);
          console.log("Exam started, UI updated");
        }, 100);
      }, 100);
    } catch (error) {
      console.error('Error starting exam:', error);
      alert("Error starting exam. Please try again.");
    } finally {
      setCreatingExam(false);
    }
  };

  // Handle answer selection
  const handleAnswerSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!currentQuestion || examCompleted) return;

    const questionId = currentQuestion.id;
    const answer = event.target.value;

    console.log(`Selected answer for question ${questionId}: ${answer}`);
    console.log(`Current question:`, currentQuestion);

    setAnswers({
      ...answers,
      [questionId]: answer,
    });

    console.log(`Updated answers:`, {...answers, [questionId]: answer});
  };

  // Toggle flagged question
  const toggleFlaggedQuestion = (questionId: number) => {
    if (flaggedQuestions.includes(questionId)) {
      setFlaggedQuestions(flaggedQuestions.filter(id => id !== questionId));
    } else {
      setFlaggedQuestions([...flaggedQuestions, questionId]);
    }
  };

  // Move to next question
  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      // Record time spent on current question before moving
      recordTimeSpent();
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  // Move to previous question
  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      // Record time spent on current question before moving
      recordTimeSpent();
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  // Track exam submission state
  const [submittingExam, setSubmittingExam] = useState(false);

  // Submit exam and redirect to results page
  const submitExam = async () => {
    console.log("Submit exam function called");
    console.log("User:", user);
    console.log("ExamId:", examId);
    console.log("Questions length:", questions.length);

    if (!user || !examId || questions.length === 0) {
      console.error("Cannot submit exam: missing user, examId, or questions");
      console.error("Details:", { user, examId, questionsLength: questions.length });
      alert("Cannot submit exam due to missing data. Please try again.");
      return;
    }

    // Record time spent on the current question before submitting
    recordTimeSpent();

    // Set submitting state to true
    console.log("Setting submitting state to true");
    setSubmittingExam(true);

    // Close the dialog with a slight delay to ensure proper UI update
    console.log("Closing submit dialog");
    setTimeout(() => {
      setShowSubmitDialog(false);
    }, 100);

    try {
      // Calculate results directly
      let correctCount = 0;

      // Count correct answers
      for (const question of questions) {
        const selectedAnswer = answers[question.id] || '';

        // Find the option key (A, B, C, D) that matches the selected answer
        let selectedKey = '';
        if (question.options) {
          for (const [key, value] of Object.entries(question.options)) {
            if (value === selectedAnswer) {
              selectedKey = key;
              break;
            }
          }
        }

        // Check if the answer is correct by comparing either the value or the key
        const isCorrect =
          selectedAnswer === question.answer || // Compare values
          selectedKey === question.answer;      // Compare keys (A, B, C, D)

        console.log(`Question ${question.id}: Selected=${selectedAnswer}, Key=${selectedKey}, Correct=${question.answer}, isCorrect=${isCorrect}`);

        if (isCorrect) {
          correctCount++;
        }
      }

      // Calculate score as percentage
      const score = (correctCount / questions.length) * 100;

      // Create a basic exam results object that matches the API response format
      const examResultsData = {
        score,
        correct_answers: correctCount, // Use snake_case to match API
        correctAnswers: correctCount,  // Keep camelCase for backward compatibility
        total_questions: questions.length, // Use snake_case to match API
        totalQuestions: questions.length,  // Keep camelCase for backward compatibility
        id: examId,                    // Use id instead of examId to match API
        examId: examId,                // Keep examId for backward compatibility
        course_id: courseId && courseId !== 'note-generated' ? parseInt(courseId) : 0, // Use snake_case to match API
        courseId: courseId && courseId !== 'note-generated' ? parseInt(courseId) : 0,  // Keep camelCase for backward compatibility
        courseName: jobId ? 'Generated Questions' : (course?.name || "Course"),
        end_time: new Date().toISOString(), // Use end_time to match API
        timestamp: new Date().toISOString(), // Keep timestamp for backward compatibility
      };

      console.log("Exam results data:", examResultsData);

      // Store results in React Query cache
      queryClient.setQueryData(['examResults', examId], examResultsData);

      // Also store in localStorage as a fallback
      localStorage.setItem('currentExamResults', JSON.stringify(examResultsData));

      // Clear current exam data from localStorage
      localStorage.removeItem('currentExamId');
      localStorage.removeItem('examStarted');
      localStorage.removeItem('currentQuestionIndex');
      localStorage.removeItem('examAnswers');
      localStorage.removeItem('flaggedQuestions');
      localStorage.removeItem('timeSpent');
      localStorage.removeItem('examTimeRemaining');
      localStorage.removeItem('examStartTimestamp');

      // Submit data to backend and wait for it to complete
      console.log("Submitting data to backend");
      await submitToBackend(correctCount, score);
      console.log("Backend submission completed");

      // Set exam as completed
      setExamCompleted(true);

      // Update the exam cache with the complete data
      queryClient.invalidateQueries({ queryKey: ['exam', examId] });

      // Prefetch the exam with attempts to ensure it's in the cache
      try {
        await queryClient.prefetchQuery({
          queryKey: ['exam', examId],
          queryFn: () => getExamWithAttempts(examId!),
        });
        console.log("Successfully prefetched exam data for results page");
      } catch (error) {
        console.error("Error prefetching exam data:", error);
      }

      // Redirect to results page
      console.log("Redirecting to results page with examId:", examId);

      // Navigate to the results page
      navigate(`/mcq/results/${examId}`);
    } catch (error) {
      console.error('Error submitting exam:', error);

      // Create fallback results even in case of error
      const fallbackExamResultsData = {
        score: 0,
        correct_answers: 0, // Use snake_case to match API
        correctAnswers: 0,  // Keep camelCase for backward compatibility
        total_questions: questions.length, // Use snake_case to match API
        totalQuestions: questions.length,  // Keep camelCase for backward compatibility
        id: examId || 0,                   // Use id instead of examId to match API
        examId: examId || 0,               // Keep examId for backward compatibility
        course_id: courseId && courseId !== 'note-generated' ? parseInt(courseId) : 0, // Use snake_case to match API
        courseId: courseId && courseId !== 'note-generated' ? parseInt(courseId) : 0,  // Keep camelCase for backward compatibility
        courseName: jobId ? 'Generated Questions' : (course?.name || "Course"),
        end_time: new Date().toISOString(), // Use end_time to match API
        timestamp: new Date().toISOString(), // Keep timestamp for backward compatibility
      };

      // Store fallback results in React Query cache
      queryClient.setQueryData(['examResults', examId], fallbackExamResultsData);

      // Also store in localStorage as a fallback
      localStorage.setItem('currentExamResults', JSON.stringify(fallbackExamResultsData));

      // Show error message
      alert("There was an error submitting your exam. You will be redirected to the results page.");

      // Redirect to results page
      console.log("Redirecting to results page with examId (error case):", examId);

      // Navigate to the results page
      navigate(`/mcq/results/${examId}`);
    }
  };

  // Submit to backend in the background
  const submitToBackend = async (correctCount: number, score: number) => {
    try {
      console.log("Submitting to backend in the background");

      if (!examId) {
        console.error("Cannot submit to backend: missing examId");
        return;
      }

      // Update exam first to ensure it's marked as completed
      const updateData: ExamUpdate = {
        score,
        correct_answers: correctCount,
        end_time: new Date().toISOString(),
      };

      try {
        console.log("Updating exam with data:", updateData);
        const updatedExam = await updateExamMutation.mutateAsync({
          id: examId,
          data: updateData
        });
        console.log("Exam updated successfully:", updatedExam);
      } catch (error) {
        console.error('Error updating exam:', error);
      }

      // Submit attempts
      for (const question of questions) {
        const selectedAnswer = answers[question.id] || '';

        // Find the option key (A, B, C, D) that matches the selected answer
        let selectedKey = '';
        if (question.options) {
          for (const [key, value] of Object.entries(question.options)) {
            if (value === selectedAnswer) {
              selectedKey = key;
              break;
            }
          }
        }

        // Check if the answer is correct by comparing either the value or the key
        const isCorrect =
          selectedAnswer === question.answer || // Compare values
          selectedKey === question.answer;      // Compare keys (A, B, C, D)

        const questionTimeSpent = timeSpent[question.id] || 0;

        const attemptData: ExamAttemptCreate = {
          exam_id: examId,
          question_id: question.id,
          is_correct: isCorrect,
          selected_answer: selectedAnswer,
          time_spent_seconds: questionTimeSpent,
        };

        try {
          console.log(`Submitting attempt for question ${question.id}:`, attemptData);
          console.log(`Using endpoint: /student-progress/exams/attempts`);
          const result = await createExamAttemptMutation.mutateAsync(attemptData);
          console.log(`Attempt submission successful:`, result);
        } catch (error) {
          console.error(`Error submitting attempt for question ${question.id}:`, error);
          // Log more detailed error information
          if (error.response) {
            console.error('Status:', error.response.status);
            console.error('Data:', error.response.data);
            console.error('Headers:', error.response.headers);
          } else if (error.request) {
            console.error('No response received. Request:', error.request);
          } else {
            console.error('Error message:', error.message);
          }
        }
      }
    } catch (error) {
      console.error('Error submitting to backend:', error);
    }
  };

  // Handle time up
  const handleTimeUp = () => {
    setShowTimeUpDialog(false);
    // Use setTimeout to avoid state update conflicts
    setTimeout(() => {
      submitExam();
    }, 0);
  };

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'default';
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.2,
        staggerChildren: 0.05,
      },
    },
    exit: { opacity: 0, transition: { duration: 0.1 } },
  };

  const itemVariants = {
    hidden: { y: 10, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'tween',
        duration: 0.2,
      },
    },
  };

  // Loading state
  if (isLoadingCourse || isLoadingQuestions) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Error state
  if (courseError || questionsError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading exam. Please try again.
      </Alert>
    );
  }

  // No questions state
  if (questions.length === 0) {
    return (
      <Box>
        <Alert severity="info" sx={{ mb: 3 }}>
          No questions available for this course. Please select a different course.
        </Alert>
      </Box>
    );
  }



  // Exam results view - simplified version
  if (examCompleted && examResults) {
    console.log("Rendering exam results:", examResults);

    // Return a simple results page
    return (
      <Box sx={{
        width: '100%',
        maxWidth: '1200px',
        mx: 'auto',
        p: 3,
        bgcolor: 'background.paper',
        borderRadius: 2,
        boxShadow: 3,
        mt: 4
      }}>
        <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
          Exam Results
        </Typography>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ my: 4, textAlign: 'center' }}>
          <Typography variant="h5" gutterBottom>
            {localStorage.getItem('noteCourseName') || (jobId ? 'Generated Questions' : course?.name)} Exam
          </Typography>

          <Box sx={{
            display: 'inline-flex',
            position: 'relative',
            borderRadius: '50%',
            width: 200,
            height: 200,
            bgcolor: examResults.score >= 70 ? 'success.light' : examResults.score >= 50 ? 'warning.light' : 'error.light',
            justifyContent: 'center',
            alignItems: 'center',
            my: 3
          }}>
            <Typography variant="h2" fontWeight="bold" color="white">
              {Math.round(examResults.score)}%
            </Typography>
          </Box>

          <Typography variant="h6" gutterBottom>
            {examResults.correctAnswers} out of {examResults.totalQuestions} correct
          </Typography>

          <Typography variant="body1" paragraph>
            {examResults.score >= 70
              ? 'Excellent work! You have a good understanding of this subject.'
              : examResults.score >= 50
              ? 'Good effort! Keep practicing to improve your score.'
              : 'You need more practice with this subject. Don\'t give up!'}
          </Typography>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Box sx={{ my: 3 }}>
          <Typography variant="h5" gutterBottom>
            Summary
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Score Details
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Score:</Typography>
                  <Typography fontWeight="bold">{Math.round(examResults.score)}%</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Grade:</Typography>
                  <Typography fontWeight="bold">
                    {examResults.score >= 90 ? 'A' :
                     examResults.score >= 80 ? 'B' :
                     examResults.score >= 70 ? 'C' :
                     examResults.score >= 60 ? 'D' : 'F'}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Correct:</Typography>
                  <Typography fontWeight="bold" color="success.main">
                    {examResults.correctAnswers}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Incorrect:</Typography>
                  <Typography fontWeight="bold" color="error.main">
                    {examResults.totalQuestions - examResults.correctAnswers}
                  </Typography>
                </Box>
              </Paper>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Paper sx={{ p: 2 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Exam Details
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Exam ID:</Typography>
                  <Typography fontWeight="bold">{examResults.examId}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Course:</Typography>
                  <Typography fontWeight="bold">{localStorage.getItem('noteCourseName') || (jobId ? 'Generated Questions' : (course?.name || 'Unknown'))}</Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Date:</Typography>
                  <Typography fontWeight="bold">
                    {new Date(examResults.timestamp).toLocaleDateString()}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Time:</Typography>
                  <Typography fontWeight="bold">
                    {new Date(examResults.timestamp).toLocaleTimeString()}
                  </Typography>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 4 }}>
          <Button
            variant="outlined"
            onClick={() => navigate('/mcq')}
            size="large"
          >
            Back to Dashboard
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate(jobId ? `/mcq/practice/note-generated?jobId=${jobId}` : (courseId && courseId !== 'note-generated' ? `/mcq/practice/${courseId}` : '/mcq'))}
            size="large"
          >
            Practice More
          </Button>
        </Box>
      </Box>
    );
  }

  return (
    <Box
      sx={{ pt: 2 }}
      key={`exam-container-${examStarted}-${examId}`}
    >
      {/* Show title only if exam not started */}
      {!examStarted && !isLoadingQuestions && (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 4, justifyContent: 'center' }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            Exam Mode: {localStorage.getItem('noteCourseName') || (jobId ? 'Generated Questions' : course?.name)}
          </Typography>
        </Box>
      )}

      {/* Show loading indicator when exam is starting */}
      {creatingExam && (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', height: '50vh' }}>
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Starting exam...
          </Typography>
        </Box>
      )}

      {/* Exam Content - Only show if exam is started and not creating */}
      {examStarted && !creatingExam && (
        <Box key={`exam-content-${examId}-${currentQuestionIndex}`}>
          {/* Exam Header */}
          <Paper
            elevation={2}
            sx={{
              p: { xs: 1.5, sm: 2 },
              mb: 2,
              borderRadius: 2,
              position: 'sticky',
              top: 0,
              zIndex: 10,
              bgcolor: theme.palette.background.paper,
              width: '100%',
              boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
            }}
          >
            <Box sx={{
              display: 'flex',
              flexDirection: { xs: 'column', sm: 'row' },
              justifyContent: 'space-between',
              alignItems: { xs: 'flex-start', sm: 'center' },
              gap: { xs: 1, sm: 2 }
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Typography variant="subtitle1" fontWeight="bold">
                  Q{currentQuestionIndex + 1}/{questions.length}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  {localStorage.getItem('noteCourseName') || (jobId ? 'Generated Questions' : course?.name)}
                </Typography>
              </Box>

              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                width: { xs: '100%', sm: 'auto' },
                justifyContent: { xs: 'space-between', sm: 'flex-end' }
              }}>
                <Chip
                  icon={<TimerIcon fontSize="small" />}
                  label={formatTimeRemaining()}
                  color={timerWarning ? 'error' : 'default'}
                  size="small"
                  sx={{
                    mr: 1.5,
                    fontWeight: 'bold',
                    borderRadius: '12px',
                    boxShadow: timerWarning ? '0 0 8px rgba(211, 47, 47, 0.5)' : 'none',
                    ...(timerWarning && {
                      animation: 'pulse 1.5s infinite',
                      '@keyframes pulse': {
                        '0%': { opacity: 1, transform: 'scale(1)' },
                        '50%': { opacity: 0.8, transform: 'scale(1.05)' },
                        '100%': { opacity: 1, transform: 'scale(1)' },
                      },
                    }),
                  }}
                />

                <Button
                  variant="contained"
                  color="primary"
                  size="small"
                  onClick={() => {
                    console.log("Submit button clicked");
                    // Use setTimeout to ensure state update happens after current execution
                    setTimeout(() => {
                      setShowSubmitDialog(true);
                    }, 0);
                  }}
                  disabled={submittingExam}
                  sx={{
                    borderRadius: '16px',
                    px: 2,
                    py: 0.5,
                    fontWeight: 'bold',
                    pointerEvents: 'auto',
                  }}
                >
                  {submittingExam ? 'Submitting...' : 'Submit'}
                </Button>
              </Box>
            </Box>

            <LinearProgress
              variant="determinate"
              value={(currentQuestionIndex / (questions.length - 1)) * 100}
              sx={{
                mt: 1,
                height: 4,
                borderRadius: 2,
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                '& .MuiLinearProgress-bar': {
                  borderRadius: 2,
                }
              }}
            />
          </Paper>

          {/* Navigation and Progress */}
          <Paper
            elevation={1}
            sx={{
              p: { xs: 1, sm: 1.5 },
              mb: 2,
              borderRadius: 2,
              boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
            }}
          >
            <Box sx={{
              display: 'flex',
              width: '100%',
              alignItems: 'center',
            }}>
              <IconButton
                onClick={handlePreviousQuestion}
                disabled={currentQuestionIndex === 0}
                size="small"
                sx={{ mr: 0.5, pointerEvents: 'auto' }}
              >
                <ArrowBackIcon fontSize="small" />
              </IconButton>

              <Box sx={{
                flex: 1,
                mx: 0.5,
                overflowX: 'auto',
                pointerEvents: 'auto',
                '&::-webkit-scrollbar': {
                  height: '4px',
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: 'transparent',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
                  borderRadius: '10px',
                },
              }}>
                <Box sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  justifyContent: 'center',
                  gap: 0.5,
                  py: 0.5,
                  pointerEvents: 'auto',
                }}>
                  {questions.map((question, index) => {
                    const isActive = index === currentQuestionIndex;
                    const isAnswered = !!answers[question.id];
                    const isFlagged = flaggedQuestions.includes(question.id);

                    return (
                      <Box
                        key={question.id}
                        onClick={() => setCurrentQuestionIndex(index)}
                        sx={{
                          width: { xs: '28px', sm: '30px' },
                          height: { xs: '28px', sm: '30px' },
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          borderRadius: '50%',
                          cursor: 'pointer',
                          fontWeight: 'bold',
                          fontSize: '0.75rem',
                          transition: 'all 0.2s ease',
                          position: 'relative',
                          boxShadow: isActive ? '0 2px 4px rgba(0,0,0,0.1)' : 'none',
                          bgcolor: isActive
                            ? theme.palette.primary.main
                            : isAnswered
                              ? theme.palette.success.light
                              : theme.palette.mode === 'dark'
                                ? 'rgba(255,255,255,0.1)'
                                : 'rgba(0,0,0,0.05)',
                          color: isActive
                            ? '#fff'
                            : isAnswered
                              ? theme.palette.success.contrastText
                              : theme.palette.text.primary,
                          border: isFlagged
                            ? `1px solid ${theme.palette.error.main}`
                            : 'none',
                          '&:hover': {
                            transform: 'scale(1.05)',
                          },
                          pointerEvents: 'auto',
                        }}
                      >
                        {isFlagged ? (
                          <FlagIcon sx={{ fontSize: '0.75rem' }} color="error" />
                        ) : (
                          index + 1
                        )}
                      </Box>
                    );
                  })}
                </Box>
              </Box>

              <IconButton
                onClick={handleNextQuestion}
                disabled={currentQuestionIndex === questions.length - 1}
                size="small"
                sx={{ ml: 0.5, pointerEvents: 'auto' }}
              >
                <ArrowForwardIcon fontSize="small" />
              </IconButton>
            </Box>

            <Box sx={{
              mt: 1,
              display: 'flex',
              justifyContent: 'space-between',
              flexWrap: 'wrap',
              borderTop: `1px solid ${theme.palette.divider}`,
              pt: 1,
              px: 0.5,
              fontSize: '0.75rem'
            }}>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                mb: { xs: 0.5, sm: 0 },
              }}>
                <CheckIcon fontSize="inherit" color="success" sx={{ mr: 0.5 }} />
                <Typography variant="caption" color="text.secondary">
                  {Object.keys(answers).length}/{questions.length} answered
                </Typography>
              </Box>

              <Box sx={{
                display: 'flex',
                alignItems: 'center',
              }}>
                <FlagIcon fontSize="inherit" color="error" sx={{ mr: 0.5 }} />
                <Typography variant="caption" color="text.secondary">
                  {flaggedQuestions.length} flagged
                </Typography>
              </Box>
            </Box>
          </Paper>

          {/* Question Card */}
          <AnimatePresence mode="wait" initial={false}>
            <motion.div
              key={`question-${currentQuestionIndex}-${examId}`}
              variants={containerVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
            >
              <MotionCard
                variants={itemVariants}
                sx={{
                  mb: 3,
                  borderRadius: 3,
                  boxShadow: '0 8px 24px rgba(0,0,0,0.08)',
                  overflow: 'hidden',
                  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)'}`,
                }}
              >
                <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
                  {currentQuestion ? (
                    <>
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'flex-end',
                        alignItems: 'center',
                        mb: 1.5,
                      }}>
                        <IconButton
                          color="warning"
                          size="small"
                          onClick={() => toggleFlaggedQuestion(currentQuestion.id)}
                          sx={{
                            p: 0.5,
                            color: flaggedQuestions.includes(currentQuestion.id) ? theme.palette.warning.main : 'inherit',
                            pointerEvents: 'auto',
                          }}
                        >
                          <FlagIcon fontSize="small" />
                        </IconButton>
                      </Box>

                      <Paper
                        elevation={0}
                        sx={{
                          p: { xs: 1.5, sm: 2 },
                          mb: 2,
                          borderRadius: 2,
                          bgcolor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)',
                          border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)'}`,
                        }}
                      >
                        <Box sx={{ lineHeight: 1.5, fontWeight: 500, mb: 2 }}>
                          <MathMarkdown>{currentQuestion.content}</MathMarkdown>
                        </Box>

                        {/* Question Image */}
                        {currentQuestion.media_url && (
                          <Box sx={{ mb: 2, textAlign: 'center' }}>
                            <img
                              src={currentQuestion.media_url}
                              alt="Question diagram"
                              style={{
                                maxWidth: '100%',
                                maxHeight: '400px',
                                borderRadius: '8px',
                                border: `1px solid ${theme.palette.divider}`,
                              }}
                              onError={(e) => {
                                console.error('Failed to load question image:', currentQuestion.media_url);
                                e.currentTarget.style.display = 'none';
                              }}
                            />
                          </Box>
                        )}
                      </Paper>

                      <FormControl component="fieldset" sx={{ width: '100%', mt: 1 }}>
                        <Typography variant="body2" fontWeight="medium" sx={{ mb: 1 }}>
                          Select your answer:
                        </Typography>
                        <RadioGroup
                          value={answers[currentQuestion.id] || ''}
                          onChange={handleAnswerSelect}
                        >
                          {currentQuestion.options ? (
                            Object.entries(currentQuestion.options).map(([key, value]) => (
                              <Paper
                                key={key}
                                elevation={0}
                                sx={{
                                  mb: 1,
                                  borderRadius: 1,
                                  overflow: 'hidden',
                                  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.08)'}`,
                                  transition: 'all 0.2s ease',
                                  '&:hover': {
                                    borderColor: theme.palette.primary.main,
                                  },
                                }}
                              >
                                <FormControlLabel
                                  value={value}
                                  control={
                                    <Radio
                                      size="small"
                                      sx={{
                                        '& .MuiSvgIcon-root': {
                                          fontSize: 18,
                                        },
                                        pointerEvents: 'auto',
                                      }}
                                    />
                                  }
                                  label={
                                    <div style={{ display: 'flex', alignItems: 'center', pointerEvents: 'auto' }}>
                                      <Typography
                                        variant="caption"
                                        fontWeight="bold"
                                        sx={{
                                          mr: 1,
                                          color: theme.palette.primary.main,
                                          width: 18,
                                          height: 18,
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'center',
                                          borderRadius: '50%',
                                          bgcolor: theme.palette.mode === 'dark' ? 'rgba(0, 127, 255, 0.1)' : 'rgba(0, 127, 255, 0.1)',
                                        }}
                                      >
                                        {key}
                                      </Typography>
                                      <div style={{ flex: 1 }}>
                                        <MathMarkdown>{value}</MathMarkdown>
                                      </div>
                                    </div>
                                  }
                                  sx={{
                                    m: 0,
                                    p: 1,
                                    width: '100%',
                                    pointerEvents: 'auto',
                                    '&:hover': {
                                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                                    },
                                  }}
                                />
                              </Paper>
                            ))
                          ) : (
                            <Alert severity="error" sx={{ mt: 1 }}>
                              No options available for this question
                            </Alert>
                          )}
                        </RadioGroup>
                      </FormControl>
                    </>
                  ) : (
                    <Box sx={{ p: 4, textAlign: 'center' }}>
                      <Typography color="error" variant="h6" gutterBottom>
                        Question data is not available
                      </Typography>
                      <Typography color="textSecondary">
                        There might be an issue loading the question data. Please try refreshing the page.
                      </Typography>
                    </Box>
                  )}

                  <Box sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    mt: 2,
                    pt: 1.5,
                    borderTop: `1px solid ${theme.palette.divider}`,
                    pointerEvents: 'auto',
                  }}>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<ArrowBackIcon fontSize="small" />}
                      onClick={handlePreviousQuestion}
                      disabled={currentQuestionIndex === 0}
                      sx={{
                        borderRadius: '16px',
                        minWidth: '80px',
                        pointerEvents: 'auto',
                      }}
                    >
                      Prev
                    </Button>

                    {currentQuestionIndex === questions.length - 1 ? (
                      <Button
                        variant="contained"
                        color="primary"
                        size="small"
                        onClick={() => {
                          console.log("Submit button (bottom) clicked");
                          // Use setTimeout to ensure state update happens after current execution
                          setTimeout(() => {
                            setShowSubmitDialog(true);
                          }, 0);
                        }}
                        disabled={submittingExam}
                        sx={{
                          borderRadius: '16px',
                          minWidth: '80px',
                          fontWeight: 'bold',
                          pointerEvents: 'auto',
                        }}
                      >
                        {submittingExam ? 'Submitting...' : 'Submit'}
                      </Button>
                    ) : (
                      <Button
                        variant="contained"
                        size="small"
                        endIcon={<ArrowForwardIcon fontSize="small" />}
                        onClick={handleNextQuestion}
                        sx={{
                          borderRadius: '16px',
                          minWidth: '80px',
                          pointerEvents: 'auto',
                        }}
                      >
                        Next
                      </Button>
                    )}
                  </Box>
                </CardContent>
              </MotionCard>
            </motion.div>
          </AnimatePresence>
        </Box>
      )}

      {/* Start Exam Dialog */}
      <Dialog
        open={showStartDialog}
        onClose={() => {
          // Only allow closing if exam hasn't started
          if (!examStarted && !creatingExam) {
            navigate('/mcq');
          }
        }}
        maxWidth="sm"
        fullWidth
        disableEscapeKeyDown={examStarted || creatingExam}
        hideBackdrop={examStarted}
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle sx={{
          bgcolor: theme.palette.primary.main,
          color: '#fff',
          py: 2,
          px: 3,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          fontSize: '1.25rem',
          fontWeight: 'bold'
        }}>
          <TimerIcon sx={{ mr: 1 }} /> Start Exam
        </DialogTitle>
        <DialogContent sx={{ p: 3, mt: 1 }}>
          <Typography variant="h6" gutterBottom>
            You are about to start an exam for <strong>{jobId ? 'Generated Questions' : course?.name}</strong>.
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Please review the exam details below before starting.
          </Typography>

          <Paper
            elevation={0}
            sx={{
              p: 3,
              mt: 2,
              mb: 2,
              borderRadius: 2,
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)',
              border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)'}`,
            }}
          >
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              Exam Details:
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
              <Box sx={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                bgcolor: theme.palette.primary.main,
                color: '#fff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              }}>
                {questions.length}
              </Box>
              <Typography variant="body1">
                Total Questions
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
              <Box sx={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                bgcolor: theme.palette.warning.main,
                color: '#fff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              }}>
                60
              </Box>
              <Typography variant="body1">
                Time Limit (minutes)
              </Typography>
            </Box>

            <Divider sx={{ my: 2 }} />

            <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
              Rules:
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <CheckIcon color="success" sx={{ mr: 1 }} />
              <Typography variant="body2">
                You can navigate between questions
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <CheckIcon color="success" sx={{ mr: 1 }} />
              <Typography variant="body2">
                You can flag questions for review
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <WarningIcon color="error" sx={{ mr: 1 }} />
              <Typography variant="body2" fontWeight="medium">
                Once submitted, you cannot retake this exam
              </Typography>
            </Box>
          </Paper>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
          <Button
            onClick={() => navigate('/mcq')}
            variant="outlined"
            sx={{
              borderRadius: '20px',
              px: 3,
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              console.log("Start Exam button clicked");
              startExam();
            }}
            disabled={creatingExam}
            startIcon={creatingExam ? <CircularProgress size={20} /> : null}
            autoFocus
            sx={{
              borderRadius: '20px',
              px: 3,
              fontWeight: 'bold',
              boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
              transition: 'all 0.3s ease',
              '&:not(:disabled):hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 6px 12px rgba(0,0,0,0.15)',
              },
            }}
          >
            {creatingExam ? 'Starting...' : 'Start Exam'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Submit Exam Dialog */}
      <Dialog
        open={showSubmitDialog}
        onClose={() => {
          if (!submittingExam) {
            console.log("Dialog close event triggered");
            setShowSubmitDialog(false);
          }
        }}
        disableEscapeKeyDown={submittingExam}
        maxWidth="sm"
        fullWidth
        sx={{ zIndex: 1400 }}
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle sx={{
          bgcolor: theme.palette.warning.main,
          color: '#fff',
          py: 2,
          px: 3,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          fontSize: '1.25rem',
          fontWeight: 'bold'
        }}>
          <WarningIcon sx={{ mr: 1 }} /> Submit Exam
        </DialogTitle>
        <DialogContent sx={{ p: 3, mt: 1 }}>
          <Typography variant="h6" gutterBottom>
            Are you sure you want to submit your exam?
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Please review your progress before submitting.
          </Typography>

          <Paper
            elevation={0}
            sx={{
              p: 3,
              mt: 2,
              mb: 2,
              borderRadius: 2,
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)',
              border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)'}`,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: theme.palette.success.main,
                color: '#fff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              }}>
                <CheckIcon />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Answered
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {Object.keys(answers).length} of {questions.length} questions
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: theme.palette.error.main,
                color: '#fff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              }}>
                {questions.length - Object.keys(answers).length}
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Unanswered
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {questions.length - Object.keys(answers).length} questions
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: theme.palette.warning.main,
                color: '#fff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              }}>
                <FlagIcon />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Flagged for review
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {flaggedQuestions.length} questions
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: theme.palette.info.main,
                color: '#fff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              }}>
                <TimerIcon />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Time remaining
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {formatTimeRemaining()}
                </Typography>
              </Box>
            </Box>
          </Paper>

          {flaggedQuestions.length > 0 && (
            <Alert
              severity="warning"
              sx={{
                mt: 2,
                borderRadius: 2,
                '& .MuiAlert-icon': {
                  alignItems: 'center'
                }
              }}
              icon={<FlagIcon fontSize="inherit" />}
            >
              <AlertTitle>Flagged Questions</AlertTitle>
              You have flagged questions that you may want to review before submitting.
            </Alert>
          )}

          {questions.length - Object.keys(answers).length > 0 && (
            <Alert
              severity="warning"
              sx={{
                mt: 2,
                borderRadius: 2,
                '& .MuiAlert-icon': {
                  alignItems: 'center'
                }
              }}
            >
              <AlertTitle>Unanswered Questions</AlertTitle>
              You have unanswered questions. Unanswered questions will be marked as incorrect.
            </Alert>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
          <Button
            onClick={() => setShowSubmitDialog(false)}
            disabled={submittingExam}
            variant="outlined"
            sx={{
              borderRadius: '20px',
              px: 3,
            }}
          >
            Continue Exam
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={submitExam}
            disabled={submittingExam}
            startIcon={submittingExam ? <CircularProgress size={20} /> : null}
            autoFocus
            sx={{
              borderRadius: '20px',
              px: 3,
              fontWeight: 'bold',
              boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
              transition: 'all 0.3s ease',
              '&:not(:disabled):hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 6px 12px rgba(0,0,0,0.15)',
              },
            }}
          >
            {submittingExam ? 'Submitting...' : 'Submit Exam'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Time Up Dialog */}
      <Dialog
        open={showTimeUpDialog}
        onClose={handleTimeUp}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle sx={{
          bgcolor: theme.palette.error.main,
          color: '#fff',
          py: 2,
          px: 3,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          fontSize: '1.25rem',
          fontWeight: 'bold'
        }}>
          <TimerIcon sx={{ mr: 1 }} /> Time's Up!
        </DialogTitle>
        <DialogContent sx={{ p: 3, mt: 1 }}>
          <Typography variant="h6" gutterBottom>
            Your exam time has expired.
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Your answers will be submitted automatically.
          </Typography>

          <Paper
            elevation={0}
            sx={{
              p: 3,
              mt: 2,
              mb: 2,
              borderRadius: 2,
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.03)' : 'rgba(0,0,0,0.02)',
              border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.05)'}`,
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: theme.palette.success.main,
                color: '#fff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              }}>
                <CheckIcon />
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Answered
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {Object.keys(answers).length} of {questions.length} questions
                </Typography>
              </Box>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Box sx={{
                width: 40,
                height: 40,
                borderRadius: '50%',
                bgcolor: theme.palette.error.main,
                color: '#fff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
                boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              }}>
                {questions.length - Object.keys(answers).length}
              </Box>
              <Box>
                <Typography variant="body2" color="text.secondary">
                  Unanswered
                </Typography>
                <Typography variant="h6" fontWeight="bold">
                  {questions.length - Object.keys(answers).length} questions
                </Typography>
              </Box>
            </Box>
          </Paper>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
          <Button
            variant="contained"
            color="primary"
            onClick={handleTimeUp}
            disabled={submittingExam}
            startIcon={submittingExam ? <CircularProgress size={20} /> : null}
            autoFocus
            sx={{
              borderRadius: '20px',
              px: 3,
              fontWeight: 'bold',
              boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
              transition: 'all 0.3s ease',
              '&:not(:disabled):hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 6px 12px rgba(0,0,0,0.15)',
              },
            }}
          >
            {submittingExam ? 'Submitting...' : 'View Results'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Calculator */}
      <DraggableCalculator initialPosition={{ x: 20, y: window.innerHeight - 80 }} />


    </Box>
  );
};

export default ExamMode;
