import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  TextField,
  InputAdornment,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Tooltip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Checkbox,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  Upload as UploadIcon,
  Image as ImageIcon,
  GetApp as ExportIcon,
  MoreVert as MoreIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';

import {
  getAdminFlashCards,
  deleteAdminFlashCard,
  bulkDeleteAdminFlashCards,
  AdminFlashCardWithCourse,
} from '../../api/adminFlashcards';
import { getCourses } from '../../api/courses';
import PageHeader from '../../components/Layout/PageHeader';
import CSVUploadDialog from '../../components/AdminFlashCards/CSVUploadDialog';
import BulkImageUploadDialog from '../../components/AdminFlashCards/BulkImageUploadDialog';

const AdminFlashCardsList: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // State
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState<number | ''>('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string | ''>('');
  const [selectedFlashcards, setSelectedFlashcards] = useState<number[]>([]);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [flashcardToDelete, setFlashcardToDelete] = useState<number | null>(null);
  const [csvUploadOpen, setCsvUploadOpen] = useState(false);
  const [bulkImageUploadOpen, setBulkImageUploadOpen] = useState(false);

  // Fetch flashcards
  const {
    data: flashcards = [],
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['adminFlashcards', page, rowsPerPage, searchTerm, selectedCourse, selectedDifficulty],
    queryFn: () =>
      getAdminFlashCards({
        skip: page * rowsPerPage,
        limit: rowsPerPage,
        search: searchTerm || undefined,
        course_id: selectedCourse || undefined,
        difficulty: selectedDifficulty || undefined,
      }),
  });

  // Fetch courses for filter
  const { data: courses = [] } = useQuery({
    queryKey: ['courses'],
    queryFn: () => getCourses(),
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: deleteAdminFlashCard,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminFlashcards'] });
      setDeleteDialogOpen(false);
      setFlashcardToDelete(null);
    },
  });

  // Bulk delete mutation
  const bulkDeleteMutation = useMutation({
    mutationFn: bulkDeleteAdminFlashCards,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminFlashcards'] });
      setSelectedFlashcards([]);
    },
  });

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleCourseFilterChange = (event: any) => {
    setSelectedCourse(event.target.value);
    setPage(0);
  };

  const handleDifficultyFilterChange = (event: any) => {
    setSelectedDifficulty(event.target.value);
    setPage(0);
  };

  const handleSelectFlashcard = (id: number) => {
    setSelectedFlashcards(prev =>
      prev.includes(id) ? prev.filter(fId => fId !== id) : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedFlashcards.length === flashcards.length) {
      setSelectedFlashcards([]);
    } else {
      setSelectedFlashcards(flashcards.map(f => f.id));
    }
  };

  const handleDeleteClick = (id: number) => {
    setFlashcardToDelete(id);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (flashcardToDelete) {
      deleteMutation.mutate(flashcardToDelete);
    }
  };

  const handleBulkDelete = () => {
    if (selectedFlashcards.length > 0) {
      bulkDeleteMutation.mutate(selectedFlashcards);
    }
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'default';
    }
  };

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Failed to load flashcards. Please try again.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <PageHeader
        title="Flashcards Management"
        subtitle="Manage admin flashcards for courses"
        action={
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Button
              variant="outlined"
              startIcon={<UploadIcon />}
              onClick={() => setCsvUploadOpen(true)}
              size={isMobile ? 'small' : 'medium'}
            >
              Upload CSV
            </Button>
            <Button
              variant="outlined"
              color="secondary"
              startIcon={<ImageIcon />}
              onClick={() => setBulkImageUploadOpen(true)}
              size={isMobile ? 'small' : 'medium'}
            >
              Bulk Images
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/admin/flashcards/new')}
              size={isMobile ? 'small' : 'medium'}
            >
              Add Flashcard
            </Button>
          </Box>
        }
      />

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
          <TextField
            placeholder="Search flashcards..."
            value={searchTerm}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 250 }}
            size="small"
          />

          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>Course</InputLabel>
            <Select
              value={selectedCourse}
              onChange={handleCourseFilterChange}
              label="Course"
            >
              <MenuItem value="">All Courses</MenuItem>
              {courses.map((course) => (
                <MenuItem key={course.id} value={course.id}>
                  {course.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Difficulty</InputLabel>
            <Select
              value={selectedDifficulty}
              onChange={handleDifficultyFilterChange}
              label="Difficulty"
            >
              <MenuItem value="">All</MenuItem>
              <MenuItem value="easy">Easy</MenuItem>
              <MenuItem value="medium">Medium</MenuItem>
              <MenuItem value="hard">Hard</MenuItem>
            </Select>
          </FormControl>

          {selectedFlashcards.length > 0 && (
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleBulkDelete}
              disabled={bulkDeleteMutation.isPending}
            >
              Delete Selected ({selectedFlashcards.length})
            </Button>
          )}
        </Box>
      </Paper>

      {/* Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={
                      selectedFlashcards.length > 0 && selectedFlashcards.length < flashcards.length
                    }
                    checked={flashcards.length > 0 && selectedFlashcards.length === flashcards.length}
                    onChange={handleSelectAll}
                  />
                </TableCell>
                <TableCell>Front Content</TableCell>
                <TableCell>Course</TableCell>
                <TableCell>Topic</TableCell>
                <TableCell>Difficulty</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : flashcards.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                    <Typography color="text.secondary">
                      No flashcards found
                    </Typography>
                  </TableCell>
                </TableRow>
              ) : (
                flashcards.map((flashcard) => (
                  <TableRow key={flashcard.id} hover>
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={selectedFlashcards.includes(flashcard.id)}
                        onChange={() => handleSelectFlashcard(flashcard.id)}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ maxWidth: 200 }}>
                        <Typography variant="body2" noWrap>
                          {flashcard.front_content}
                        </Typography>
                        {flashcard.media_url && (
                          <Chip
                            icon={<ImageIcon />}
                            label="Has Image"
                            size="small"
                            variant="outlined"
                            sx={{ mt: 0.5 }}
                          />
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {flashcard.course_name || 'Unknown'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {flashcard.course_code}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      {flashcard.topic && (
                        <Chip label={flashcard.topic} size="small" variant="outlined" />
                      )}
                    </TableCell>
                    <TableCell>
                      {flashcard.difficulty && (
                        <Chip
                          label={flashcard.difficulty}
                          size="small"
                          color={getDifficultyColor(flashcard.difficulty) as any}
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={flashcard.is_active ? 'Active' : 'Inactive'}
                        size="small"
                        color={flashcard.is_active ? 'success' : 'default'}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <Tooltip title="View">
                          <IconButton
                            size="small"
                            onClick={() => navigate(`/admin/flashcards/${flashcard.id}`)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit">
                          <IconButton
                            size="small"
                            onClick={() => navigate(`/admin/flashcards/${flashcard.id}/edit`)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDeleteClick(flashcard.id)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>

        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={-1} // We don't have total count from API
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this flashcard? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* CSV Upload Dialog */}
      <CSVUploadDialog
        open={csvUploadOpen}
        onClose={() => setCsvUploadOpen(false)}
        onSuccess={() => {
          setCsvUploadOpen(false);
          refetch();
        }}
      />

      {/* Bulk Image Upload Dialog */}
      <BulkImageUploadDialog
        open={bulkImageUploadOpen}
        onClose={() => setBulkImageUploadOpen(false)}
        onSuccess={() => {
          setBulkImageUploadOpen(false);
          refetch();
        }}
      />
    </Box>
  );
};

export default AdminFlashCardsList;
